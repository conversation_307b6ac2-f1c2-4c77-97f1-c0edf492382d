default:
  tags: [docker]
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

stages:
 - .pre
 - build
 - test
 - deploy
 - postdeploy

include:
 - .gitlab/common.yml # re-usable blocks that can be extended
 - .gitlab/preview.yml # preview builds and deployments

variables:
  SERVICE_NAME: cxme
  VITE_APP_BASE_URL: /
  VITE_TASKS_SERVICE_URL: "https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage"
  VITE_SUMMARIZER_SERVICE_URL: "https://s-summarizer-f497a979-c7ca7opsia-uc.a.run.app"
  VITE_CONNECTORS_SERVICE_URL: "https://s-connregistry-851510a1-5xeedb3yvq-uc.a.run.app"
  VITE_CASE_SERVICE_URL: "https://app.stage.goboomtown.com/"
  VITE_PROXY_SERVICE_URL: "https://inbox-230937369904.us-central1.run.app/proxy"

cache_dependencies:
  image: node:latest
  stage: .pre
  cache:  # Cache modules in between jobs
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - .npm/
      - node_modules/
  script:
    - echo "@services:registry=https://${CI_SERVER_HOST}/api/v4/packages/npm/" > .npmrc
    - echo "//${CI_SERVER_HOST}/:_authToken=${COMPONENT_LIBRARY_READ_REPOSITORY}" >> .npmrc
    - npm ci --cache .npm --prefer-offline
  rules:
    - !reference [.rules_deploy, rules]
    - !reference [.rules_preview, rules]

lint:
  image: node:latest
  interruptible: true
  stage: test
  cache:
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - node_modules
    policy: pull
  script:
    - npm run lint:ci
  needs: [cache_dependencies]
  rules:
    - !reference [.rules_deploy, rules]
    - !reference [.rules_preview, rules]

unit_test:
  image: node:latest
  interruptible: true
  stage: test
  cache:
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - node_modules
    policy: pull
  script:
    - npm run test:ci
  artifacts:
    when: always
    reports:
      junit: $CI_PROJECT_DIR/test-report.xml
      coverage_report:
        coverage_format: cobertura
        path: $CI_PROJECT_DIR/coverage/cobertura-coverage.xml
  timeout: 480 seconds
  needs: [cache_dependencies]
  rules:
    - !reference [.rules_deploy, rules]
    - !reference [.rules_preview, rules]

version_bump:
  image: node:lts
  stage: build
  variables:
    GIT_STRATEGY: clone
  cache:
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - node_modules
    policy: pull
  needs: [cache_dependencies]
  extends:
    - .rules_deploy
  before_script:
    - apt update && apt install -y git jq
    - git config --global user.email "${GIT_CI_USERNAME}@${CI_SERVER_HOST}"
    - git config --global user.name "OvationCXM CI"
    - git remote set-url --push origin "https://${GIT_CI_USERNAME}:${GIT_CI_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - git status --porcelain
    - git fetch
    - git checkout $CI_DEFAULT_BRANCH
    - echo "Current branch $(git rev-parse --abbrev-ref HEAD)"
    - echo "Current branch $(git branch --show-current)"
  script:
    - |
      if [[ "$CI_COMMIT_TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        ./scripts/release.sh explicit version-only
      else
        ./scripts/release.sh patch version-only
      fi
  artifacts:
    paths:
      - package.json
    expire_in: 1 hour

build_test:
  stage: build
  extends:
    - .build
    - .rules_preview

build_staging:
  stage: build
  extends:
    - .build
    - .rules_deploy
  variables:
    VITE_BOOMTOWN_API_HOST: https://api.stage.goboomtown.com
    VITE_TASKS_SERVICE_URL: "https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage"
    VITE_JOURNEYS_SERVICE_URL: "https://journeys-5xeedb3yvq-uc.a.run.app"
    VITE_NOTIFICATIONS_SERVICE_URL: "https://notify-svc-5xeedb3yvq-uc.a.run.app"
    VITE_SUMMARIZER_SERVICE_URL: "https://s-summarizer-f497a979-c7ca7opsia-uc.a.run.app"
    VITE_CONNECTORS_SERVICE_URL: "https://s-connregistry-851510a1-5xeedb3yvq-uc.a.run.app"
    NODE_ENV: development
  environment:
    name: stage
  dependencies: [version_bump]
  needs: [cache_dependencies, version_bump]

upload_staging_sourcemaps_datadog:
  stage: postdeploy
  extends:
    - .upload_sourcemaps_datadog
    - .rules_deploy
  variables:
    PUB_HOSTNAME: appv5.stage-ovationcxm.app
  environment:
    name: stage
    action: verify
  dependencies: [build_staging, version_bump]
  needs: [build_staging, version_bump]

deploy_staging:
  stage: deploy
  extends:
    - .deploy_gcs
    - .env_stage
    - .rules_deploy
  needs: [build_staging]
  resource_group: staging
  variables:
    PUB_HOSTNAME: appv5.stage-ovationcxm.app

test_staging:
  stage: postdeploy
  extends:
    - .rules_deploy
  needs: [deploy_staging]
  environment:
    name: stage
    action: verify
  script:
    - echo "Running tests"
    - echo "Running e2e tests"

build_preproduction:
  stage: build
  extends:
    - .build
    - .rules_deploy
  variables:
    VITE_BOOMTOWN_API_HOST: https://api.preprod.goboomtown.com
    VITE_TASKS_SERVICE_URL: "https://us-central1-preprod-microservices-9a06.cloudfunctions.net/service-tasks-preprod"
    VITE_SUMMARIZER_SERVICE_URL: "https://s-summarizer-627397ad-nfznudrdwa-uc.a.run.app"
    VITE_CONNECTORS_SERVICE_URL: "https://s-connregistry-627397ad-nfznudrdwa-uc.a.run.app"
    VITE_JOURNEYS_SERVICE_URL: "https://journeys-nfznudrdwa-uc.a.run.app"
    VITE_NOTIFICATIONS_SERVICE_URL: "https://notify-svc-nfznudrdwa-uc.a.run.app"
    NODE_ENV: development
  environment:
    name: preprod
  dependencies: [version_bump]
  needs: [cache_dependencies, version_bump]
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+(\-rc[0-9]+)?$/'

upload_preproduction_sourcemaps_datadog:
  stage: postdeploy
  extends: .upload_sourcemaps_datadog
  variables:
    PUB_HOSTNAME: appv5.preprod-ovationcxm.app
  environment:
    name: preprod
    action: verify
  dependencies: [build_preproduction, version_bump]
  needs: [build_preproduction, version_bump]
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+(\-rc[0-9]+)?$/'

deploy_preproduction:
  stage: deploy
  extends:
    - .deploy_gcs
    - .env_preprod
    - .rules_deploy
  needs: [build_preproduction]
  variables:
    PUB_HOSTNAME: appv5.preprod-ovationcxm.app
    VITE_BOOMTOWN_API_HOST: https://api.preprod.goboomtown.com
  resource_group: preproduction
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+(\-rc[0-9]+)?$/'

test_preproduction:
  stage: postdeploy
  extends:
    - .rules_deploy
  needs: [deploy_preproduction]
  environment:
    name: preprod
    action: verify
  script:
    - echo "Running tests"
    - echo "Running e2e tests"
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+(\-rc[0-9]+)?$/'
    
build_production:
  stage: build
  extends:
    - .build
  variables:
    VITE_BOOMTOWN_API_HOST: https://api.goboomtown.com
    VITE_TASKS_SERVICE_URL: "https://us-central1-prod-microservices-d798.cloudfunctions.net/service-tasks-prod"
    VITE_SUMMARIZER_SERVICE_URL: "https://s-summarizer-8f5419eb-7hyimkhqqq-uc.a.run.app"
    VITE_CONNECTORS_SERVICE_URL: "https://s-connregistry-8f5419eb-7hyimkhqqq-uc.a.run.app"
    VITE_JOURNEYS_SERVICE_URL: "https://journeys-7hyimkhqqq-uc.a.run.app"
    VITE_NOTIFICATIONS_SERVICE_URL: "https://notify-svc-7hyimkhqqq-uc.a.run.app"
    NODE_ENV: production
  environment:
    name: prod
  dependencies: [version_bump]
  needs: [cache_dependencies, version_bump]
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+$/'

upload_production_sourcemaps_datadog:
  stage: postdeploy
  extends: .upload_sourcemaps_datadog
  variables:
    PUB_HOSTNAME: appv5.ovationcxm.app
  environment:
    name: prod
    action: verify
  dependencies: [build_production, version_bump]
  needs: [build_production, version_bump]
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+$/'

deploy_production:
  stage: deploy
  extends:
    - .deploy_gcs
    - .env_prod
  needs: [build_production]
  variables:
    PUB_HOSTNAME: appv5.ovationcxm.app
    VITE_BOOMTOWN_API_HOST: https://api.goboomtown.com
  resource_group: production
  when: manual
  before_script:
    # Delete sourcemaps before upload
    - find . -name "*.map" -type f -delete
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+$/'

test_production:
  stage: postdeploy
  needs: [deploy_production]
  script:
    - echo "Running tests"
    - echo "Running e2e tests"
  environment:
    name: prod
    action: verify
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+$/'

# Commit version job (generate changelog, commit, tag, push)
commit_version:
  stage: build
  image: node:lts
  variables:
    GIT_STRATEGY: clone
  cache:
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - node_modules
    policy: pull
  needs:
    - job: cache_dependencies
    - job: version_bump
      artifacts: false # need a clean dir to work
  before_script:
    - apt update && apt install -y git jq
    - git config --global user.email "${GIT_CI_USERNAME}@${CI_SERVER_HOST}"
    - git config --global user.name "OvationCXM CI"
    - git remote set-url --push origin "https://${GIT_CI_USERNAME}:${GIT_CI_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - git status --porcelain
    - git fetch
    - git checkout $CI_DEFAULT_BRANCH
  script:
    - |
      if [[ "$CI_COMMIT_TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        ./scripts/release.sh explicit full
      else
        ./scripts/release.sh patch full
      fi
  dependencies:
    - version_bump
  rules:
    - !reference [.rules_deploy, rules]
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+$/'

