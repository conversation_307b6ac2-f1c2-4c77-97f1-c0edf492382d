import { defineStore } from 'pinia'
import { ref } from 'vue'
import { fetchUserTasks, fetchTeamTasks, fetchAllTasks } from '@/composables/services/useTasksApi'
import { useUserStore } from '@/stores/user'

export const useTasksStore = defineStore('tasks', () => {
  const userTasks = ref<any[]>([])
  const teamTasks = ref<any[]>([])
  const allTasks = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  async function loadUserTasks(userId: string) {
    loading.value = true
    error.value = null
    try {
      const data = await fetchUserTasks(userId)
      userTasks.value = Array.isArray(data) ? data : (data.tasks || [])
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load user tasks'
    } finally {
      loading.value = false
    }
  }

  async function loadTeamTasks() {
    loading.value = true
    error.value = null
    try {
      const userStore = useUserStore()
      
      // Check if we have teams data
      if (!userStore.userTeams || !Array.isArray(userStore.userTeams) || userStore.userTeams.length === 0) {
        console.log('📋 TasksStore: No teams available for loading team tasks')
        teamTasks.value = []
        return
      }

      console.log('📋 TasksStore: Loading tasks for all teams:', userStore.userTeams.length, 'teams')
      
      // Make parallel API calls for all teams
      const teamTaskPromises = userStore.userTeams.map(async (team: any) => {
        const teamId = team?.team_id || team // Handle both object and string formats
        if (!teamId) {
          console.warn('📋 TasksStore: Skipping team with no ID:', team)
          return []
        }
        
        try {
          console.log('📋 TasksStore: Fetching tasks for team:', teamId)
          const data = await fetchTeamTasks(teamId)
          const tasks = Array.isArray(data) ? data : (data.tasks || [])
          console.log('📋 TasksStore: Received', tasks.length, 'tasks for team:', teamId)
          return tasks
        } catch (err) {
          console.error('📋 TasksStore: Failed to load tasks for team:', teamId, err)
          return [] // Return empty array for failed requests so other teams still work
        }
      })

      // Wait for all team task requests to complete
      const allTeamTasks = await Promise.all(teamTaskPromises)
      
      // Flatten the array of arrays into a single array
      const combinedTasks = allTeamTasks.flat()
      
      console.log('📋 TasksStore: Combined tasks from all teams:', combinedTasks.length, 'total tasks')
      console.log('📋 TasksStore: TEAM tasks breakdown by status:', 
        combinedTasks.reduce((acc: any, task: any) => {
          acc[task.status] = (acc[task.status] || 0) + 1
          return acc
        }, {})
      )
      teamTasks.value = combinedTasks
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load team tasks'
      console.error('📋 TasksStore: Error in loadTeamTasks:', err)
    } finally {
      loading.value = false
    }
  }

  async function loadAllTasks() {
    loading.value = true
    error.value = null
    try {
      const userStore = useUserStore()
      const orgId = userStore.userData?.object_id || ''
      console.log('📋 TasksStore: Fetching ALL tasks for orgId:', orgId)
      const data = await fetchAllTasks(orgId)
      const tasks = Array.isArray(data) ? data : (data.tasks || [])
      console.log('📋 TasksStore: Received ALL tasks:', tasks.length, 'total tasks')
      console.log('📋 TasksStore: ALL tasks breakdown by status:', 
        tasks.reduce((acc: any, task: any) => {
          acc[task.status] = (acc[task.status] || 0) + 1
          return acc
        }, {})
      )
      allTasks.value = tasks
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load all tasks'
      console.error('📋 TasksStore: Error in loadAllTasks:', err)
    } finally {
      loading.value = false
    }
  }

  return {
    userTasks,
    teamTasks,
    allTasks,
    loading,
    error,
    loadUserTasks,
    loadTeamTasks,
    loadAllTasks
  }
}) 