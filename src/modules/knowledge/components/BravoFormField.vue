<script setup lang="ts">
import { ref, watch, onBeforeUnmount, onMounted, nextTick, useSlots } from 'vue';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import MultiSelect from 'primevue/multiselect';
import Dropdown from 'primevue/dropdown';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoSelectField from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoDatePicker from '@services/ui-component-library/components/BravoDatePicker.vue';
import ArticleLabelsInput from './ArticleLabelsInput.vue';

// Define the input types we support
type InputType = 'text' | 'dropdown' | 'multiselect' | 'articlelabels' | 'date' | 'datetime';
type DisplayType = 'text' | 'chips' | 'tag';

const props = defineProps<{
  label: string;
  fieldName: string;
  value: any;
  displayValue: string | string[];
  inputType: InputType;
  displayType: DisplayType; 
  options?: any[];
  optionLabel?: string;
  optionValue?: string;
  isLoading?: boolean;
  isHorizontal?: boolean;
  iconClass?: string;
  isSaving?: boolean;
  noValueText?: string;
  dataTestId?: string;
  filterPlaceholder?: string;
  showFilter?: boolean;
  enforceSubmitButton?: boolean;
  isEditing?: boolean;
  libraryId?: string;
  showClear?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update', fieldName: string, value: any): void;
  (e: 'save', fieldName: string, value: any): void;
  (e: 'submit-start'): void;
  (e: 'cancel'): void;
  (e: 'filter', event: any): void;
}>();

const slots = useSlots();

const isEditingInternal = ref(false);
const editedValue = ref<any>(null);
const multiSelectRef = ref<any>(null);
const dropdownRef = ref<any>(null);
const labelsInputRef = ref<any>(null);
const textInputRef = ref<any>(null);
const datePickerRef = ref<any>(null);
const inputWidth = ref('240px'); // Default fallback width
const fieldValueRef = ref<HTMLElement | null>(null);

// Initialize the edited value when the props value changes
watch(() => props.value, (newValue) => {
  // if (!isEditing.value) {
    // Make a deep copy of array values to avoid reference issues
    editedValue.value = Array.isArray(newValue) ? [...newValue] : newValue;
  // }
}, { immediate: true });

// Start editing the field
const startEditing = () => {
  // Only allow editing if isEditing prop is true (not undefined or false)
  if (props.isEditing !== true) return;
  
  // Make a fresh copy of the value to edit
  editedValue.value = Array.isArray(props.value) ? [...props.value] : props.value;
  isEditingInternal.value = true;
  emit('update', props.fieldName, editedValue.value);
  
  // For dropdown inputs, automatically click to open after a short delay
  if (props.inputType === 'dropdown') {
    setTimeout(() => {
      if (dropdownRef.value && dropdownRef.value.$el) {
        // Try to find the clickable element within the dropdown component
        const clickableElement = dropdownRef.value.$el.querySelector('.p-dropdown, .p-select') || dropdownRef.value.$el;
        if (clickableElement) {
          clickableElement.click();
        }
      }
    }, 100);
  }
  
  // For multiselect inputs, automatically click to open after a short delay
  if (props.inputType === 'multiselect') {
    setTimeout(() => {
      if (multiSelectRef.value && multiSelectRef.value.$el) {
        // Try to find the clickable element within the multiselect component
        const clickableElement = multiSelectRef.value.$el.querySelector('.p-multiselect') || multiSelectRef.value.$el;
        if (clickableElement) {
          clickableElement.click();
        }
      }
    }, 100);
  }
  
  // For article labels inputs, automatically click to open after a short delay
  if (props.inputType === 'articlelabels') {
    setTimeout(() => {
      if (labelsInputRef.value && labelsInputRef.value.$el) {
        // Try to find the clickable element within the labels component
        const clickableElement = labelsInputRef.value.$el.querySelector('.p-treeselect') || labelsInputRef.value.$el;
        if (clickableElement) {
          clickableElement.click();
        }
      }
    }, 100);
  }
  
  // For text inputs, focus after a short delay
  if (props.inputType === 'text') {
    setTimeout(() => {
      if (textInputRef.value && textInputRef.value.$el) {
        // Try to find the input element within the text component
        const inputElement = textInputRef.value.$el.querySelector('input') || textInputRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For date inputs, focus after a short delay
  if (props.inputType === 'date' || props.inputType === 'datetime') {
    setTimeout(() => {
      if (datePickerRef.value && datePickerRef.value.$el) {
        // Try to find the input element within the date picker component
        const inputElement = datePickerRef.value.$el.querySelector('input') || datePickerRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
};

// Save the edited value
const saveEdit = () => {
  // Ensure we pass the correct data type for arrays
  const valueToSave = editedValue.value;
  
  // Emit that submission is starting
  emit('submit-start');
  
  // Emit the save event with the value
  emit('save', props.fieldName, valueToSave);
  
  // NOTE: We don't exit edit mode here anymore
  // isEditing.value = false;
  // This will be handled by the parent component when it calls our "handleSaveComplete" method
};

// Handle dropdown change - automatically save when user selects an option
const handleDropdownChange = () => {
  // Add a small delay to ensure the value is updated
  setTimeout(() => {
    saveEdit();
  }, 50);
};

// Handle multiselect change - automatically save when user changes selection
const handleMultiselectChange = () => {
  // Add a small delay to ensure the value is updated
  setTimeout(() => {
    saveEdit();
  }, 50);
};

// New method to handle save completion
const handleSaveComplete = (success: boolean = true) => {
  if (success) {
    isEditingInternal.value = false;
  }
  // If not successful, stay in edit mode
};

// Cancel editing
const cancelEdit = () => {
  // Reset the edited value to the original value
  editedValue.value = Array.isArray(props.value) ? [...props.value] : props.value;
  isEditingInternal.value = false;
  emit('cancel');
};

// Helper function to check if an element is part of a multiselect dropdown
const isInsideMultiSelect = (element: HTMLElement | null): boolean => {
  if (!element) return false;
  
  // Check for common MultiSelect class names in the element or its parents (PrimeVue and UI library)
  return Boolean(
    element.classList.contains('p-multiselect-item') || 
    element.classList.contains('p-checkbox') ||
    element.classList.contains('p-multiselect-header') ||
    element.classList.contains('p-multiselect-filter-container') ||
    element.classList.contains('p-dropdown-item') ||
    element.classList.contains('p-selectbutton-button') ||
    element.hasAttribute('data-pc-section') ||
    element.hasAttribute('data-pc-name') ||
    (element.parentElement && isInsideMultiSelect(element.parentElement))
  );
};

// Handle click outside - only save automatically if enforceSubmitButton is false
const handleClickOutside = (event: MouseEvent) => {
  if (isEditingInternal.value) {
    const target = event.target as HTMLElement;
    const editField = document.querySelector(`.${props.fieldName}-edit-field`);
    
    // Check for various dropdown panel classes (PrimeVue and UI library)
    const dropdownPanels = document.querySelectorAll('.p-dropdown-panel, .p-multiselect-panel, .p-overlay-panel, [data-pc-section="panel"]');
    const isInsideDropdownPanel = Array.from(dropdownPanels).some(panel => panel.contains(target));
    
    // Check if clicked element is inside the multiselect items
    const isMultiSelectItem = isInsideMultiSelect(target);
    
    // Check if target is inside any overlay or portal element
    const isInsideOverlay = target.closest('.p-component-overlay, .p-overlaypanel, [data-pc-name="overlaypanel"], [data-pc-name="dropdown"], [data-pc-name="multiselect"]');
    
    // Don't close if we're clicking inside the MultiSelect items, panel, overlay, or our edit field
    if (editField && !editField.contains(target) && 
        !isInsideDropdownPanel &&
        !isMultiSelectItem &&
        !isInsideOverlay) {
      
      // Auto-save for text inputs, dropdown, and multiselect (unless enforceSubmitButton is true)
      const shouldAutoSave = props.inputType === 'text' || 
                           props.inputType === 'dropdown' || 
                           props.inputType === 'multiselect';
      
      if (shouldAutoSave && !props.enforceSubmitButton) {
        // Auto-save these input types when clicking outside
        saveEdit();
      } else if (!props.enforceSubmitButton) {
        // Only auto-save if explicit submission is not required
        saveEdit();
      } else {
        // For enforceSubmitButton mode, just exit edit mode without reverting changes
        // The user must explicitly cancel (Escape or X button) to revert changes
        isEditingInternal.value = false;
      }
    }
  }
};

// Handle keyboard events
const handleKeyDown = (event: KeyboardEvent) => {
  if (isEditingInternal.value && event.key === 'Escape') {
    // Escape key should always cancel and revert changes
    cancelEdit();
  } else if (isEditingInternal.value && event.key === 'Enter' && props.inputType === 'text') {
    // Enter key should save for text inputs
    event.preventDefault();
    saveEdit();
  }
};

// Add click and keyboard event listeners when editing starts
watch(isEditingInternal, (newValue) => {
  if (newValue) {
    // Use a slight delay to add the click listener to avoid immediate triggering
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }, 100);
  } else {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeyDown);
  }
});

// Clean up event listeners when component is unmounted
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('keydown', handleKeyDown);
});



const clearFilter = () => {
  if (multiSelectRef.value && typeof multiSelectRef.value.filterValue !== 'undefined') {
    multiSelectRef.value.filterValue = '';
  }
};

// Calculate input width based on field-value container width minus 60px
const calculateInputWidth = () => {
  if (fieldValueRef.value) {
    const fieldWidth = fieldValueRef.value.offsetWidth;
    const calculatedWidth = Math.max(fieldWidth - 60, 180); // Minimum 180px
    inputWidth.value = `${calculatedWidth}px`;
  }
};

// Set up ResizeObserver to watch for field width changes
let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  await nextTick();
  calculateInputWidth();
  
  // Set up ResizeObserver to reactively update width
  if (fieldValueRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      calculateInputWidth();
    });
    resizeObserver.observe(fieldValueRef.value);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});

defineExpose({
  handleSaveComplete,
  clearFilter,
  startEditing
});
</script>

<template>
  <div 
    class="data-field" 
    :class="{ 'horizontal': isHorizontal }" 
    :data-testid="dataTestId || `article-${fieldName}-field`"
  >
    <BravoLabel :text="label" class="article-field-label" />
    <div 
      ref="fieldValueRef"
      :class="[
        'field-value', 
        'editable-field', 
        `${fieldName}-edit-field`,
        { 'field-section-disabled': isEditing !== true },
        { 'editing': isEditingInternal }
      ]" 
      @click="startEditing"
      :data-testid="`article-${fieldName}-value`"
    >
      <!-- Display mode -->
      <div 
        v-if="!isEditingInternal" 
        class="display-mode" 
        :class="[
          `${fieldName}-display-mode`,
          { 'field-disabled': isEditing !== true }
        ]" 
        @click.stop="startEditing"
      >
        <i v-if="iconClass" :class="iconClass" />
        
        <!-- Custom display content via slot -->
        <slot v-if="slots.display" name="display" :displayValue="displayValue" :value="value" />
        
        <!-- Text display -->
        <span v-else-if="displayType === 'text'" class="clickable-value">
          {{ typeof displayValue === 'string' ? displayValue : Array.isArray(displayValue) ? displayValue.join(', ') : (noValueText || 'No value') }}
        </span>
        
        <!-- Chips display -->
        <div v-else-if="displayType === 'chips'" class="chips-content">
          <span v-if="!Array.isArray(displayValue) || displayValue.length === 0">{{ noValueText || 'No items' }}</span>
          <div v-else class="chip-items">
            <BravoTag
              v-for="(item, index) in displayValue"
              :key="index"
              :value="item"
              severity="info"
              class="chip-item"
              @click.stop="startEditing"
              :data-testid="`${fieldName}-chip-${index}`"
            />
          </div>
        </div>
        
        <!-- Tag display (single value with icon) -->
        <div v-else-if="displayType === 'tag'" class="tag-content">
          <span>{{ displayValue }}</span>
        </div>
      </div>
      
      <!-- Edit mode -->
      <div v-else class="edit-container-compact">
        <div class="input-wrapper" :style="{ width: inputWidth }">
          <!-- Text input -->
          <BravoInputText 
            v-if="inputType === 'text'"
            ref="textInputRef"
            v-model="editedValue"
            class="edit-input"
            @click.stop
            @keydown.enter.prevent="saveEdit"
            :data-testid="`${dataTestId || fieldName}-input`"
          />
          
          <!-- Dropdown input with custom templates - use native PrimeVue Dropdown -->
          <Dropdown
            v-if="inputType === 'dropdown' && (slots.option || slots.footer)"
            ref="dropdownRef"
            v-model="editedValue"
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            :showClear="showClear"
            class="edit-dropdown"
            :data-testid="`${dataTestId || fieldName}-dropdown`"
            @click.stop
            @change="handleDropdownChange"
          >
            <!-- Pass through custom option template if provided -->
            <template v-if="slots.option" #option="slotProps">
              <slot name="option" :option="slotProps.option" />
            </template>
            <!-- Pass through footer template if provided -->
            <template v-if="slots.footer" #footer>
              <slot name="footer" />
            </template>
          </Dropdown>
          
          <!-- Dropdown input without custom templates - use BravoSelectField -->
          <BravoSelectField 
            v-else-if="inputType === 'dropdown'"
            ref="dropdownRef"
            :id="`${dataTestId || fieldName}-dropdown`"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            class="edit-dropdown"
            :dataTestId="`${dataTestId || fieldName}-dropdown`"
            :showClear="showClear"
            @click.stop
            @change="handleDropdownChange"
          />
          
          <!-- MultiSelect input -->
          <MultiSelect 
            v-else-if="inputType === 'multiselect'"
            ref="multiSelectRef"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            display="chip"
            :maxSelectedLabels="3"
            class="edit-multiselect"
            :loading="isLoading"
            :filter="showFilter !== false"
            :filterPlaceholder="filterPlaceholder || `Search ${label}`"
            :data-testid="`${dataTestId || fieldName}-multiselect`"
            @filter="$emit('filter', $event)"
            @click.stop
            @change="handleMultiselectChange"
          >
            <!-- Pass through custom option template if provided -->
            <template v-if="slots.option" #option="slotProps">
              <slot name="option" :option="slotProps.option" />
            </template>
            <!-- Pass through footer template if provided -->
            <template v-if="slots.footer" #footer>
              <slot name="footer" />
            </template>
          </MultiSelect>
          
          <!-- ArticleLabelsInput for labels -->
          <ArticleLabelsInput
            v-else-if="inputType === 'articlelabels'"
            v-model="editedValue"
            :library-id="libraryId || 'default'"
            placeholder="Select labels"
            class="w-full edit-articlelabels"
            :disabled="!isEditing"
            :data-testid="`${dataTestId || fieldName}-articlelabels`"
            @click.stop
          />
          
          <!-- Date input -->
          <BravoDatePicker
            v-else-if="inputType === 'date'"
            ref="datePickerRef"
            v-model="editedValue"
            dateFormat="mm/dd/yy"
            class="edit-date"
            :data-testid="`${dataTestId || fieldName}-date`"
            @click.stop
          />
          
          <!-- DateTime input -->
          <BravoDatePicker
            v-else-if="inputType === 'datetime'"
            ref="datePickerRef"
            v-model="editedValue"
            dateFormat="mm/dd/yy"
            :showTime="true"
            class="edit-datetime"
            :data-testid="`${dataTestId || fieldName}-datetime`"
            @click.stop
          />

        </div>
        
        <div class="edit-actions-vertical">
          <Button
            v-if="!isSaving"
            icon="pi pi-check"
            class="p-button-text"
            @mousedown.stop
            @click.stop="saveEdit"
            :data-testid="`save-${dataTestId}-btn`"
          />
          <Button
            v-if="!isSaving"
            icon="pi pi-times"
            class="p-button-text p-button-danger"
            @mousedown.stop
            @click.stop="cancelEdit"
            :data-testid="`cancel-${dataTestId}-edit-btn`"
          />
          <ProgressSpinner
            v-if="isSaving"
            style="width: 1.5rem; height: 1.5rem"
            strokeWidth="4"
            :data-testid="`${dataTestId}-saving-spinner`"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.data-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.data-field.horizontal {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
}

.data-field.horizontal .field-value {
  flex: 0 0 70%;
}

.data-field .field-value {
  font-size: 1rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: .5rem;
  flex-wrap: wrap;
  width: 100%;
}

.data-field .field-value i {
  color: #64748b;
  font-size: 1rem;
}

.field-value.editable-field {
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: -6px -8px;
  padding: 6px 8px;
  border-radius: 8px;
  width: 100%;
}

.field-value.editable-field:not(.editing):hover {
  background-color: rgba(0, 0, 0, 0.05);
}
  
/* Prevent hover effect when field contains disabled content */
.field-value.editable-field:hover:has(.field-disabled) {
  background-color: transparent !important;
}

.edit-container-compact {
  display: block;
  width: 100%;
  position: relative;
}

.input-wrapper {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  overflow: hidden;
}

.edit-input {
  width: 100%;
  font-size: 0.813rem;
  box-sizing: border-box;
}

.edit-dropdown, .edit-multiselect, .edit-date, .edit-datetime {
  width: 100%;
  font-size: 0.813rem;
}

/* Fix PrimeVue components width */
.edit-dropdown :deep(.p-dropdown),
.edit-dropdown :deep(.p-select),
.edit-multiselect :deep(.p-multiselect),
.edit-date :deep(.p-calendar),
.edit-datetime :deep(.p-calendar) {
  width: 100% !important;
  max-width: 100%;
}

/* Add margin around inputs for proper spacing */
.edit-dropdown,
.edit-multiselect,
.edit-input,
.edit-articlelabels,
.edit-date,
.edit-datetime {
  margin: 2px;
}

/* Ensure overflow is visible to prevent cutoff */
.input-wrapper,
.edit-container-compact {
  overflow: visible !important;
}

.edit-dropdown :deep(.p-dropdown),
.edit-dropdown :deep(.p-select),
.edit-multiselect :deep(.p-multiselect),
.edit-date :deep(.p-calendar),
.edit-datetime :deep(.p-calendar) {
  overflow: visible !important;
}



/* Control multiselect chips container */
.edit-multiselect :deep(.p-multiselect-label) {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Prevent multiselect chips from wrapping */
.edit-multiselect :deep(.p-multiselect-token-label) {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}



.edit-actions-vertical {
  display: inline-flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  width: auto;
  height: 36px; /* Fixed height to match typical input height */
  min-height: 36px;
}

.edit-actions-vertical button {
  flex: 0 0 32px;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.display-mode {
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
  border-radius: 8px;
  padding: 0px;
  gap: 0.5rem;
}

.display-mode i {
  margin-top: 2px;
}

.clickable-value {
  cursor: pointer;
}

.chips-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.chips-content > span {
  font-size: 14px;
}

.chip-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.chip-item {
  cursor: pointer;
  transition: transform 0.1s ease;
}

.chip-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* PrimeVue Multiselect Filter Styling */
:deep(.p-multiselect-filter-container) {
  margin-bottom: 0.5rem;
}

:deep(.p-multiselect-filter) {
  width: 100%;
  padding: 0.5rem;
}

:deep(.p-multiselect-panel) {
  max-height: 400px;
  overflow-y: auto;
}

// Add these new styles for BravoLabel
.article-field-label {
  flex: 0 0 30%;
  display: block;
}

.data-field.horizontal .article-field-label {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  margin-right: 1rem;
}

/* Disabled field styles */
.field-disabled {
    cursor: default !important;
    opacity: 1;
    pointer-events: none;
}

.field-disabled:hover {
    background-color: transparent !important;
}

/* Also prevent hover on chip items when disabled */
.field-disabled .chip-item:hover {
    transform: none !important;
}

/* Prevent pointer cursor on disabled fields and all their children */
.field-disabled,
.field-disabled *,
.field-disabled .clickable-value,
.field-disabled .chip-item,
.field-disabled .display-mode {
    cursor: default !important;
}

/* Disable entire field section when not editable */
.field-section-disabled {
    pointer-events: none !important;
    cursor: default !important;
    opacity: 1;
}

.field-section-disabled:hover {
    background-color: transparent !important;
}
</style> 