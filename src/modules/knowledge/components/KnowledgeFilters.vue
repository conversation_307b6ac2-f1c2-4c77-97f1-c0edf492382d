<!-- KnowledgeFilters.vue -->
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoIconField from '@services/ui-component-library/components/BravoIconField.vue';
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';
import FilterDropdown from './FilterDropdown.vue';
import { useI18n } from 'vue-i18n';
import ArchiveArticlesDialog from './dialogs/ArchiveArticles.vue';
import UnpublishArticlesDialog from './dialogs/UnpublishArticles.vue';
import PublishArticlesDialog from './dialogs/PublishArticles.vue';
import ShareArticlesDialog from './dialogs/ShareArticles.vue';
import UnshareArticlesDialog from './dialogs/UnshareArticles.vue';
import UpdateArticlesTagDialog from './dialogs/UpdateArticlesTag.vue';
import UpdateProductsDialog from './dialogs/UpdateProducts.vue';
import UpdateAccessControlsDialog from './dialogs/UpdateAccessControls.vue';
import UpdateLabelsDialog from './dialogs/UpdateLabels.vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { usePermissions } from '@/composables/usePermissions';

const props = defineProps<{
  isLoading?: boolean;
  currentPage?: number;
  rowsPerPage?: number;
  sortField?: string;
  sortOrder?: number;
  selectedArticlesCount?: number;
  selectedArticles?: any[];
}>();

const emit = defineEmits<{
  (e: 'search', params: any): void; 
  (e: 'reset'): void;
}>();

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const store = useKnowledgeStore();
const { can } = usePermissions();
// Search related
const searchInputValue = ref('');
const searchDelayTimeout = ref<number | null>(null);
const isUpdatingFromUrl = ref(false);

// Status and Access filter
const activeFilters = ref<string[]>([]);
const statusValues = ref<string[]>([]);
const accessValues = ref<string[]>([]);
const filterMenu = ref();
const actionsMenu = ref();

// Filter types for the dropdown
const filterTypes = [
  { label: 'Status', value: 'status', icon: 'pi pi-tag' },
  { label: 'Access', value: 'access', icon: 'pi pi-lock' }
];

// Filter options
const statusOptions = [
  { label: 'Draft', value: '1' },
  { label: 'Published', value: '0' },
  { label: 'Archived', value: '98' }
];

const accessOptions = [
  { label: 'Public', value: 'public' },
  { label: 'Private', value: 'private' },
  { label: 'Internal', value: 'internal' }
];

// Computed to show active filter count
const activeFilterCount = computed(() => {
  let count = 0;
  if (statusValues.value.length > 0) count++;
  if (accessValues.value.length > 0) count++;
  return count;
});

const menuItems: { label: string; value: string; icon: string; command: () => void }[] = [];

if (can.editArticle()) {
  menuItems.push(...[{
    label: t('knowledge.actionsMenu.update_labels'),
    value: 'label',
    icon: 'pi pi-tag',
    command: () => onUpdateActionClick('label')
  },
  {
    label: t('knowledge.actionsMenu.update_access_controls'),
    value: 'access',
    icon: 'pi pi-lock',
    command: () => onUpdateActionClick('access')
  },
  {
    label: t('knowledge.actionsMenu.add_rem_products'),
    value: 'products',
    icon: 'pi pi-box',
    command: () => onUpdateActionClick('products')
  },
  {
    label: t('knowledge.actionsMenu.add_rem_tags'),
    value: 'tags',
    icon: 'pi pi-tags',
    command: () => onUpdateActionClick('tags')
  },
  {
    label: t('knowledge.actionsMenu.share_with_orgs'),
    value: 'share',
    icon: 'pi pi-share-alt',
    command: () => onUpdateActionClick('share')
  },
  {
    label: t('knowledge.actionsMenu.unshare_from_orgs'),
    value: 'unshare',
    icon: 'pi pi-share-alt',
    command: () => onUpdateActionClick('unshare')
  }]);
}

if (can.publishArticle()) {
  menuItems.push(...[{
    label: t('knowledge.actionsMenu.publish'),
    value: 'publish',
    icon: 'pi pi-check-square',
    command: () => onUpdateActionClick('publish')
  },
  {
    label: t('knowledge.actionsMenu.unpublish'),
    value: 'unpublish',
    icon: 'pi pi-times-circle',
    command: () => onUpdateActionClick('unpublish')
  }]);
}

if (can.deleteArticle()) {
  menuItems.push(...[{
    label: t('knowledge.actionsMenu.archive'),
    value: 'archive',
    icon: 'pi pi-inbox',
    command: () => onUpdateActionClick('archive')
  }]);
}

const actionsMenuItems = computed(() => {
  return menuItems;
});

// Dialog visibility states
const showLabelsDialog = ref(false);
const showAccessControlsDialog = ref(false);
const showProductsDialog = ref(false);
const showTagsDialog = ref(false);
const showShareDialog = ref(false);
const showUnshareDialog = ref(false);
const showPublishDialog = ref(false);
const showUnpublishDialog = ref(false);
const showArchiveDialog = ref(false);

// Handle label update action
const handleLabelUpdate = (labels: string[]) => {
  setTimeout(() => {
    performSearch();
  }, 1000);
};

// Handle access controls update
const handleAccessControlsUpdate = (data: { accessLevel: string; teamAccessTo: string[] }) => {
  performSearch();
};

// Handle products update
const handleProductsUpdate = (products: string[]) => {
  performSearch();
};

// Handle tags update
const handleTagsUpdate = (tags: string[]) => {
  performSearch();
};

// Handle share articles
const handleShareArticles = (organizations: string[]) => {
  performSearch();
};

// Handle unshare articles
const handleUnshareArticles = (organizations: string[]) => {
  performSearch();
};

// Handle publish articles
const handlePublishArticles = () => {
  performSearch();
};

// Handle unpublish articles
const handleUnpublishArticles = () => {
  performSearch();
};

// Handle archive articles
const handleArchiveArticles = () => {
  performSearch();
};

// Update onUpdateActionClick function
const onUpdateActionClick = (action: string) => {
  switch (action) {
    case 'label':
      showLabelsDialog.value = true;
      break;
    case 'access':
      showAccessControlsDialog.value = true;
      break;
    case 'products':
      showProductsDialog.value = true;
      break;
    case 'tags':
      showTagsDialog.value = true;
      break;
    case 'share':
      showShareDialog.value = true;
      break;
    case 'unshare':
      showUnshareDialog.value = true;
      break;
    case 'publish':
      showPublishDialog.value = true;
      break;
    case 'unpublish':
      showUnpublishDialog.value = true;
      break;
    case 'archive':
      showArchiveDialog.value = true;
      break;
  }
};

// Function to show filter dropdown menu
const showFilterMenu = (event: Event) => {
  filterMenu.value.toggle(event);
};

// Function to show actions menu
const showActionsMenu = (event: Event) => {
  if (actionsMenuItems.value.length) {
    actionsMenu.value.toggle(event);
  }
};

// Function to show specific filter
const addFilter = (filterType: string) => {
  if (!activeFilters.value.includes(filterType)) {
    activeFilters.value.push(filterType);
  }
  filterMenu.value.toggle();
};

// Function to hide specific filter
const removeFilter = (filterType: string) => {
  // Remove the filter type from active filters
  activeFilters.value = activeFilters.value.filter(f => f !== filterType);
  
  // Trigger search with updated filters
  performSearch();
};

// Function to remove all filters and update URL
const clearAllFilters = () => {
  activeFilters.value = [];
  statusValues.value = [];
  accessValues.value = [];
  searchInputValue.value = '';
  
  // After clearing all filters, refresh the results
  emit('reset');
};

// Update URL params
const updateUrlParams = (params: any) => {
  // Skip URL update if we're currently updating from the URL
  if (isUpdatingFromUrl.value) return;
  
  const query: Record<string, any> = { ...route.query };
  
  // Update search query param
  if (searchInputValue.value) {
    query.q = searchInputValue.value;
  } else {
    delete query.q;
  }
  
  // Update status filter param
  if (statusValues.value.length > 0) {
    query.status = statusValues.value.length === 1 ? statusValues.value[0] : statusValues.value;
  } else {
    delete query.status;
  }
  
  // Update access filter param
  if (accessValues.value.length > 0) {
    query.access = accessValues.value.length === 1 ? accessValues.value[0] : accessValues.value;
  } else {
    delete query.access;
  }
  
  // Update pagination params - always include page parameter
  query.page = String(props.currentPage || 1);
  
  if (props.rowsPerPage && props.rowsPerPage !== 25) { // Only include if not default
    query.limit = props.rowsPerPage.toString();
  } else {
    delete query.limit;
  }
  
  // Update sorting params - only if a sort field is specified
  if (props.sortField && props.sortField !== '') {
    query.sortField = props.sortField;
    query.sortOrder = props.sortOrder?.toString() || '1';
    
    // For debugging - this shows the exact sort format used in the API call
    console.log('Sort in URL:', { 
      field: props.sortField, 
      order: props.sortOrder, 
      apiFormat: JSON.stringify([{
        property: props.sortField,
        direction: props.sortOrder === 1 ? 'ASC' : 'DESC'
      }])
    });
  } else {
    delete query.sortField;
    delete query.sortOrder;
  }
  
  // Replace the current URL with the new params
  router.replace({ query });
};

// Handle search input with debounce
const handleSearchInput = (event: Event) => {
  // Clear any existing timeouts
  if (searchDelayTimeout.value !== null) {
    clearTimeout(searchDelayTimeout.value);
  }
  
  // Set a new timeout for debounce (500ms)
  searchDelayTimeout.value = window.setTimeout(() => {
    performSearch();
  }, 500);
};

// Handle search on enter key
const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (searchDelayTimeout.value !== null) {
      clearTimeout(searchDelayTimeout.value);
      searchDelayTimeout.value = null;
    }
    performSearch();
  }
};

// Clear search function
const clearSearch = () => {
  searchInputValue.value = '';
  performSearch();
};

// Perform search with all filters
const performSearch = () => {
  const apiParams: any = {
    filter: [
      { property: "type", value: store.selectedContentType === 'templates' ? 1 : 0 },
      { property: "id", value: "_no_filter_" },
      { property: "parent_id", value: null },
      { property: "root_parent_id", value: null },
      { property: "partner_ids", operator: "intersect_set", value: null },
      { property: "visibility", operator: "=", value: null },
      { property: "status", operator: "=", value: null },
      { property: "owner_partner_id", value: "_no_filter_" },
      { property: "dict_id", value: null },
      { property: "tag_ids", value: null }
    ]
  };
  
  // Add query parameter if there is a search term
  if (searchInputValue.value.trim()) {
    apiParams.query = searchInputValue.value.trim();
  }
  
  // Add status filter if values are selected
  if (statusValues.value && statusValues.value.length > 0) {
    // Find and update the status filter
    const statusIndex = apiParams.filter.findIndex((f: any) => f.property === "status");
    if (statusIndex >= 0) {
      if (statusValues.value.length === 1) {
        // For single value, use "=" operator
        apiParams.filter[statusIndex].operator = "=";
        // Keep status value as string
        apiParams.filter[statusIndex].value = statusValues.value[0];
      } else {
        // For multiple values, use "in" operator
        apiParams.filter[statusIndex].operator = "in";
        // Keep status values as strings for the API
        apiParams.filter[statusIndex].value = statusValues.value;
      }
    }
  }
  
  // Add access/visibility filter if values are selected
  if (accessValues.value && accessValues.value.length > 0) {
    // Find and update the visibility filter
    const visibilityIndex = apiParams.filter.findIndex((f: any) => f.property === "visibility");
    if (visibilityIndex >= 0) {
      if (accessValues.value.length === 1) {
        // For single value, use "=" operator
        apiParams.filter[visibilityIndex].operator = "=";
        apiParams.filter[visibilityIndex].value = accessValues.value[0];
      } else {
        // For multiple values, use "in" operator
        apiParams.filter[visibilityIndex].operator = "in";
        apiParams.filter[visibilityIndex].value = accessValues.value;
      }
    }
  }
  
  // Add pagination parameters - ensure these are numbers
  const page = props.currentPage || 1;
  const limit = props.rowsPerPage || 25;
  
  apiParams.page = page;
  apiParams.start = (page - 1) * limit;
  apiParams.limit = limit;
  
  // Add sorting parameters if available
  if (props.sortField && props.sortField !== '') {
    // Format sort parameter exactly as shown in the example
    apiParams.sort = JSON.stringify([{
      property: props.sortField,
      direction: props.sortOrder === 1 ? 'ASC' : 'DESC'
    }]);
  }
  
  // Update URL params
  updateUrlParams(apiParams);
  
  // Emit search event with parameters
  emit('search', apiParams);
};

// Load params from URL
const loadParamsFromUrl = () => {
  // Set the flag to indicate we're updating from URL
  isUpdatingFromUrl.value = true;
  
  try {
    // Get search query from URL
    if (route.query.q) {
      searchInputValue.value = route.query.q as string;
    } else {
      searchInputValue.value = '';
    }
    
    // Get status filter from URL
    if (route.query.status) {
      const statusParam = route.query.status;
      if (Array.isArray(statusParam)) {
        statusValues.value = statusParam.map(s => String(s));
      } else {
        statusValues.value = [String(statusParam)];
      }
      
      if (!activeFilters.value.includes('status')) {
        activeFilters.value.push('status');
      }
    } else {
      statusValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'status');
    }
    
    // Get access filter from URL
    if (route.query.access) {
      const accessParam = route.query.access;
      if (Array.isArray(accessParam)) {
        accessValues.value = accessParam.map(a => String(a));
      } else {
        accessValues.value = [String(accessParam)];
      }
      
      if (!activeFilters.value.includes('access')) {
        activeFilters.value.push('access');
      }
    } else {
      accessValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'access');
    }
    

  } finally {
    // Reset the flag when we're done
    isUpdatingFromUrl.value = false;
  }
};

// Watch for filter changes
watch(() => statusValues.value, () => {
  if (statusValues.value.length === 0 && activeFilters.value.includes('status')) {
    // Don't remove status from active filters when deselecting all values
    // We still want to show the empty filter
  } else if (statusValues.value.length > 0 && !activeFilters.value.includes('status')) {
    // Add status to active filters when selecting values
    activeFilters.value.push('status');
  }
}, { deep: true });

watch(() => accessValues.value, () => {
  if (accessValues.value.length === 0 && activeFilters.value.includes('access')) {
    // Don't remove access from active filters when deselecting all values
    // We still want to show the empty filter
  } else if (accessValues.value.length > 0 && !activeFilters.value.includes('access')) {
    // Add access to active filters when selecting values
    activeFilters.value.push('access');
  }
}, { deep: true });

// Watch for props changes to update search
const propsChangeTimeout = ref<number | null>(null);

watch(() => [props.currentPage, props.rowsPerPage, props.sortField, props.sortOrder], () => {
  // Only trigger a search if we're not currently loading from URL
  if (!isUpdatingFromUrl.value) {
    // Clear any existing timeout to prevent duplicate calls
    if (propsChangeTimeout.value !== null) {
      clearTimeout(propsChangeTimeout.value);
    }
    
    // Set a new timeout for a very small debounce (50ms)
    // This helps prevent duplicate calls when multiple props change at once
    propsChangeTimeout.value = window.setTimeout(() => {
      performSearch();
      propsChangeTimeout.value = null;
    }, 50);
  }
}, { deep: true });

// Initialize component
loadParamsFromUrl();

// Expose methods for parent components
defineExpose({
  performSearch,
  loadParamsFromUrl,
  clearAllFilters,
  getSearchInputValue: () => searchInputValue.value,
  getStatusValues: () => statusValues.value,
  getAccessValues: () => accessValues.value,
  getActiveFilters: () => activeFilters.value
});

const searchPlaceholder = computed(() =>
  store.selectedContentType === 'templates'
    ? t('knowledge.search_templates')
    : t('knowledge.search_articles')
);
</script>

<template>
  <div class="search-row">
    <div class="search-filters">
      <BravoIconField>
        <BravoInputIcon class="pi pi-search" />
        <BravoInputText 
          v-model="searchInputValue" 
          @input="handleSearchInput" 
          @keydown="handleSearchKeydown" 
          :placeholder="searchPlaceholder" 
          :disabled="isLoading"
        />
      </BravoIconField>
      
      <!-- Add Filter dropdown -->
      <BravoButton 
        v-if="activeFilters.length < 2"
        :label="t('knowledge.add_filter')" 
        icon="pi pi-filter"
        severity="secondary"
        @click="showFilterMenu" 
        class="filter-button"
        :disabled="isLoading"
      />

      <!-- Actions Button -->
      <BravoButton 
        v-if="props.selectedArticlesCount && props.selectedArticlesCount > 0"
        :label="t('knowledge.actions')" 
        icon="pi pi-check-square"
        severity="secondary"
        @click="showActionsMenu" 
        class="actions-button"
        :disabled="isLoading"
      />
      
      <!-- Filter Menu -->
      <BravoMenu ref="filterMenu" :model="filterTypes.filter(f => !activeFilters.includes(f.value)).map(f => ({
        label: f.label,
        icon: f.icon,
        command: () => addFilter(f.value)
      }))" :popup="true" />

      <!-- Actions Menu -->
      <BravoMenu v-if="actionsMenuItems.length" ref="actionsMenu" :model="actionsMenuItems" :popup="true" />
      
      <!-- Active Filters -->
      <div v-if="activeFilters.length > 0" class="active-filters-row">
        <!-- Status Filter -->
        <div v-if="activeFilters.includes('status')" class="active-filter">
          <FilterDropdown
            label="Status"
            :options="statusOptions"
            v-model="statusValues"
            @remove="removeFilter('status')"
            @change="performSearch"
            :disabled="isLoading"
          />
        </div>
        
        <!-- Access Filter -->
        <div v-if="activeFilters.includes('access')" class="active-filter">
          <FilterDropdown
            label="Access"
            :options="accessOptions"
            v-model="accessValues"
            @remove="removeFilter('access')"
            @change="performSearch"
            :disabled="isLoading"
          />
        </div>
        
        <!-- Clear all filters button -->
        <BravoButton 
          v-if="activeFilters.length > 0"
          icon="pi pi-filter-slash"
          text
          size="small"
          @click="clearAllFilters"
          class="clear-filters-btn"
          label="Clear All"
          :disabled="isLoading"
        />
      </div>
    </div>
    <!-- Add UpdateLabelsDialog -->
    <UpdateLabelsDialog
      v-model:visible="showLabelsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleLabelUpdate"
    />

    <!-- Add UpdateAccessControlsDialog -->
    <UpdateAccessControlsDialog
      v-model:visible="showAccessControlsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleAccessControlsUpdate"
    />

    <!-- Add UpdateProductsDialog -->
    <UpdateProductsDialog
      v-model:visible="showProductsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleProductsUpdate"
    />

    <!-- Add UpdateTagsDialog -->
    <UpdateArticlesTagDialog
      v-model:visible="showTagsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleTagsUpdate"
    />

    <!-- Add ShareArticlesDialog -->
    <ShareArticlesDialog
      v-model:visible="showShareDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @share="handleShareArticles"
    />

    <!-- Add UnshareArticlesDialog -->
    <UnshareArticlesDialog
      v-model:visible="showUnshareDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @unshare="handleUnshareArticles"
    />

    <!-- Add PublishArticlesDialog -->
    <PublishArticlesDialog
      v-model:visible="showPublishDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @publish="handlePublishArticles"
    />

    <!-- Add UnpublishArticlesDialog -->
    <UnpublishArticlesDialog
      v-model:visible="showUnpublishDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @unpublish="handleUnpublishArticles"
    />

    <!-- Add ArchiveArticlesDialog -->
    <ArchiveArticlesDialog
      v-model:visible="showArchiveDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @archive="handleArchiveArticles"
    />
  </div>
</template>

<style scoped>
.search-row {
  margin-bottom: 1.5rem;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

.active-filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.active-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-filters-btn {
  margin-left: auto;
}

/* Make the filter button indicate state */
.filter-button.p-button {
  position: relative;
}

.filter-button.p-button:has(.p-badge) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}
.actions-button.p-button {
  position: relative;
  margin-left: auto;
}

.actions-button.p-button:has(.p-badge) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}
</style> 