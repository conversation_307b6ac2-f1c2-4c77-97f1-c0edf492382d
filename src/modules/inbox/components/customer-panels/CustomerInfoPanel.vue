<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useCMSStore } from '@/stores/cms'
import { useMemberStore } from '@/stores/member'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import Skeleton from 'primevue/skeleton'

interface SchemaField {
  layout_id: string
  position: number
  type: number
  field: string
  object: string
  fieldType: string
  required: boolean
  readOnly: boolean
  lbl: string
  options?: Array<{ val: string; lbl: string; id: string }>
}

const props = defineProps<{
  issue: any
}>()

const emit = defineEmits<{
  (e: 'update:issue', issue: any): void
}>()

const cmsStore = useCMSStore()
const memberStore = useMemberStore()
const loadingLayout = ref(false)
const schemaFields = ref<SchemaField[]>([])
const savingFields = ref<Record<string, boolean>>({})
const fieldRefs = ref<Record<string, any>>({})

const issue = computed(() => props.issue)
const member = computed(() => props.issue?.member || {})
const memberUser = computed(() => props.issue?.memberUser || {})
const partnerId = computed(() => props.issue?.member?.context_org_id || 'H3F')

// Load the customer layout schema when component mounts or partner changes
const loadCustomerLayout = async () => {
  if (!partnerId.value) return
  
  loadingLayout.value = true
  try {
    const layout = await cmsStore.fetchCustomerLayout(partnerId.value)
    
    if (layout?.schemaFields) {
      // Sort fields by position and filter for relevant fields
      // Only show type 1 fields (actual input fields), exclude type 2 (headers) and type 5 (computed fields)
      const filteredFields = layout.schemaFields
        .filter((field: any) => field.object === 'members' && field.type === 1)
      
      schemaFields.value = filteredFields
        .sort((a: any, b: any) => a.position - b.position)
        .map((field: any) => ({
          layout_id: field.layout_id,
          position: field.position,
          type: field.type,
          field: field.field,
          object: field.object,
          fieldType: field.fieldType || 'text',
          required: field.required || false,
          readOnly: field.readOnly || false,
          lbl: field.lbl,
          options: field.options || []
        }))
    } else {
      // Fallback to fetch all layouts if specific customer layout fails
      try {
        const allLayouts = await cmsStore.fetchAllLayouts(partnerId.value)
        const customerLayouts = allLayouts.filter(l => l.object === 'members')
        
        if (customerLayouts.length > 0) {
          const customerLayout = customerLayouts[0]
          if (customerLayout.schemaFields) {
            const filteredFields = customerLayout.schemaFields
              .filter((field: any) => field.object === 'members' && field.type === 1)
            
            schemaFields.value = filteredFields
              .sort((a: any, b: any) => a.position - b.position)
              .map((field: any) => ({
                layout_id: field.layout_id,
                position: field.position,
                type: field.type,
                field: field.field,
                object: field.object,
                fieldType: field.fieldType || 'text',
                required: field.required || false,
                readOnly: field.readOnly || false,
                lbl: field.lbl,
                options: field.options || []
              }))
          }
        }
      } catch (fallbackError) {
        console.error('CustomerInfoPanel: Error in fallback layout fetch:', fallbackError)
      }
    }
  } catch (error) {
    console.error('CustomerInfoPanel: Error loading customer layout:', error)
  } finally {
    loadingLayout.value = false
  }
}

// Get field value from member data
const getFieldValue = (field: SchemaField) => {
  // Check both member and memberUser objects for the field
  const memberValue = member.value[field.field]
  const memberUserValue = memberUser.value[field.field]
  
  return memberValue !== undefined ? memberValue : memberUserValue
}

// Get display value for field
const getFieldDisplayValue = (field: SchemaField) => {
  const value = getFieldValue(field)
  
  if (value === undefined || value === null) {
    return '—'
  }
  
  // Handle picklist fields with options
  if (field.fieldType === 'picklist' && field.options) {
    const option = field.options.find(opt => opt.val == value || opt.id == value) // Use == for loose comparison
    return option ? option.lbl : String(value)
  }
  
  // Handle date fields
  if (field.fieldType === 'date' || field.fieldType === 'datetime') {
    if (value !== null && value !== undefined && value !== '') {
      const date = new Date(value)
      if (field.fieldType === 'date') {
        return date.toLocaleDateString()
      } else {
        return date.toLocaleString()
      }
    }
  }
  
  // Handle URL fields
  if (field.fieldType === 'url' && value) {
    return String(value)
  }
  
  // Always return a string
  return String(value)
}

// Map field types to BravoFormField input types
const getInputType = (field: SchemaField) => {
  switch (field.fieldType) {
    case 'picklist':
      return 'dropdown'
    case 'date':
      return 'date'
    case 'datetime':
      return 'datetime'
    case 'text':
    case 'url':
    default:
      return 'text'
  }
}

// Handle field save using the member API
const handleFieldSave = async (fieldName: string, value: any) => {
  savingFields.value[fieldName] = true
  
  try {
    // Prepare the update parameters
    const updateParams = {
      id: member.value.id,
      context_org_id: partnerId.value,
      [fieldName]: value
    }
    
    console.log('CustomerInfoPanel: Saving field', fieldName, 'with value:', value)
    
    // Call the member update API
    const response = await memberStore.updateMember(updateParams)
    
    console.log('CustomerInfoPanel: Save successful, updating local data')
    
    // Manually trigger the switch back to display mode first
    fieldRefs.value[fieldName]?.handleSaveComplete(true)
    
    // Then update the local issue data to reflect the new value
    // Use nextTick to ensure the UI update happens after the field state change
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const updatedIssue = { ...props.issue }
    if (updatedIssue.member) {
      updatedIssue.member = { ...updatedIssue.member, [fieldName]: value }
    }
    if (updatedIssue.memberUser && updatedIssue.memberUser[fieldName] !== undefined) {
      updatedIssue.memberUser = { ...updatedIssue.memberUser, [fieldName]: value }
    }
    
    // Emit the updated issue to parent component
    emit('update:issue', updatedIssue)
    
    return true
  } catch (error) {
    console.error('CustomerInfoPanel: Error saving field:', fieldName, error)
    
    // Notify the field component of the failure
    fieldRefs.value[fieldName]?.handleSaveComplete(false)
    
    return false
  } finally {
    savingFields.value[fieldName] = false
  }
}

// Handle field updates (real-time updates while editing)
const handleFieldUpdate = (fieldName: string, value: any) => {
  // Could emit events here if needed for real-time updates
}

// Watch for partner changes and issue data - but only reload layout if partner changes or issue is initially loaded
watch([partnerId, () => props.issue?.id], ([newPartnerId, newIssueId], [oldPartnerId, oldIssueId]) => {
  // Only reload layout if:
  // 1. Partner ID changed, or
  // 2. Issue ID changed (new issue), or  
  // 3. Initial load (oldIssueId is undefined)
  if (newPartnerId && newIssueId && (
    newPartnerId !== oldPartnerId || 
    newIssueId !== oldIssueId || 
    oldIssueId === undefined
  )) {
    loadCustomerLayout()
  }
}, { immediate: true })

onMounted(() => {
  // Only load if we have both issue data and partner ID
  if (props.issue && partnerId.value) {
    loadCustomerLayout()
  }
})
</script>

<template>
  <div class="py-2">
    <!-- Loading state with skeleton -->
    <div v-if="!issue || loadingLayout" class="customer-skeleton">
      <div v-for="i in 4" :key="i" class="skeleton-field">
        <Skeleton height="0.75rem" width="30%" class="mb-1" />
        <Skeleton height="1.25rem" width="70%" />
      </div>
    </div>
    
    <!-- Schema-driven fields -->
    <div v-else-if="schemaFields.length > 0" class="customer-fields">
      <BravoFormField
        v-for="field in schemaFields"
        :key="field.field"
        :ref="el => { if (el) fieldRefs[field.field] = el }"
        :label="field.lbl"
        :fieldName="field.field"
        :value="getFieldValue(field)"
        :displayValue="getFieldDisplayValue(field)"
        :inputType="getInputType(field)"
        displayType="text"
        :options="field.options"
        optionLabel="lbl"
        optionValue="val"
        :isLoading="false"
        :isHorizontal="false"
        :isSaving="savingFields[field.field] || false"
        :isEditing="!field.readOnly"
        noValueText="—"
        :dataTestId="`customer-${field.field}`"
        @update="(fieldName, value) => handleFieldUpdate(fieldName, value)"
        @save="(fieldName, value) => handleFieldSave(fieldName, value)"
        @submit-start="() => {}"
        @cancel="() => {}"
        class="mb-3"
      >
        <!-- Custom display for URL fields -->
        <template v-if="field.fieldType === 'url'" #display="{ displayValue, value }">
          <a 
            v-if="value" 
            :href="value.startsWith('http') ? value : `https://${value}`" 
            target="_blank" 
            class="text-blue-600 hover:text-blue-800 underline"
            @click.stop
          >
            {{ displayValue }}
          </a>
          <span v-else class="text-slate-500">{{ displayValue }}</span>
        </template>
      </BravoFormField>
    </div>
  </div>
</template>

<style scoped>
.customer-fields {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.customer-fields .mb-3 {
  margin-bottom: 0.75rem;
}

.customer-fields .mb-3:last-child {
  margin-bottom: 0;
}

.customer-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}
</style> 