<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import { useMetaAPI } from '@/composables/services/useMetaAPI'
import { useUserStore } from '@/stores/user'
import type { Issue } from '@/services/IssuesAPI'

interface Props {
  label: string
  fieldName: string
  value: any
  displayValue: string | string[]
  issue?: Issue | null
  isEditing?: boolean
  isSaving?: boolean
  noValueText?: string
  dataTestId?: string
  ownerTeamValue?: string // The selected owner team ID to filter users
  isHorizontal?: boolean
}

interface Emits {
  (e: 'update', fieldName: string, value: any): void
  (e: 'save', fieldName: string, value: any): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  isEditing: true,
  isSaving: false,
  noValueText: '—',
  ownerTeamValue: undefined,
  isHorizontal: false
})

const emit = defineEmits<Emits>()

// State
const ownerUsers = ref<any[]>([])
const loading = ref(false)
const metaAPI = useMetaAPI()
const userStore = useUserStore()
const bravoFormFieldRef = ref<any>(null)

// Generate a unique instance ID for debugging
const instanceId = Math.random().toString(36).substr(2, 9)
console.log('👤 OwnerUserFormField: New instance created:', {
  instanceId,
  fieldName: props.fieldName,
  label: props.label
})

// Computed options for the field - keep all original properties
const fieldOptions = computed(() => {
  const options = ownerUsers.value.map((user: any) => ({
    ...user, // Keep all original properties including url_avatar, email, etc.
    lbl: user.lbl,
    val: user.val,
    id: user.id
  }))
  
  console.log('👤 OwnerUserFormField: Computed fieldOptions:', {
    ownerUsersLength: ownerUsers.value.length,
    optionsLength: options.length,
    options: options,
    fieldName: props.fieldName
  })
  
  return options
})

// Computed display value - show user label instead of ID
const computedDisplayValue = computed(() => {
  if (!props.value || ownerUsers.value.length === 0) {
    return props.displayValue
  }
  
  // Find the user that matches the current value
  const matchingUser = ownerUsers.value.find((user: any) => user.val === props.value)
  if (matchingUser) {
    return matchingUser.lbl
  }
  
  // Fallback to original display value
  return props.displayValue
})

// Check if current user can be assigned (is in the available users list)
const canAssignToMe = computed(() => {
  const currentUserId = userStore.userData?.id
  
  console.log(`👤 OwnerUserFormField [${instanceId}]: canAssignToMe computed:`, {
    currentUserId: currentUserId,
    ownerUsersLength: ownerUsers.value.length,
    currentValue: props.value,
    ownerUsers: ownerUsers.value,
    userStoreData: userStore.userData
  })
  
  if (!currentUserId || ownerUsers.value.length === 0) {
    console.log(`👤 OwnerUserFormField [${instanceId}]: No current user ID or no owner users`)
    return false
  }
  
  // Check if current user is in the available users list
  const currentUserInList = ownerUsers.value.find((user: any) => user.val === currentUserId || user.id === currentUserId)
  
  console.log(`👤 OwnerUserFormField [${instanceId}]: Current user in list check:`, {
    currentUserInList: currentUserInList,
    isAlreadySelected: props.value === currentUserId,
    shouldShow: !!(currentUserInList && props.value !== currentUserId)
  })
  
  // Only show if current user is in the list and not already selected
  return !!(currentUserInList && props.value !== currentUserId)
})

// Get current user data for "Assign to me" functionality
const currentUserData = computed(() => {
  const currentUserId = userStore.userData?.id
  if (!currentUserId) return null
  
  return ownerUsers.value.find((user: any) => user.val === currentUserId || user.id === currentUserId)
})

// Get the selected user's avatar for display mode
const selectedUserAvatar = computed(() => {
  if (!props.value || ownerUsers.value.length === 0) {
    return null
  }
  
  // Find the user that matches the current value
  const matchingUser = ownerUsers.value.find((user: any) => user.val === props.value)
  const avatarUrl = matchingUser?.url_avatar || matchingUser?.avatar || null
  
  console.log('👤 OwnerUserFormField: selectedUserAvatar computed:', {
    value: props.value,
    matchingUser: matchingUser,
    avatarUrl: avatarUrl,
    url_avatar: matchingUser?.url_avatar,
    avatar: matchingUser?.avatar
  })
  
  return avatarUrl
})

// Fetch owner users based on selected team
async function fetchOwnerUsers() {
  if (!props.ownerTeamValue) {
    console.log(`👤 OwnerUserFormField [${instanceId}]: No owner team selected, clearing users`)
    ownerUsers.value = []
    return
  }
  
  try {
    loading.value = true
    
    console.log(`👤 OwnerUserFormField [${instanceId}]: Fetching owner users with params:`, {
      team_ids: props.ownerTeamValue,
      fieldName: props.fieldName,
      label: props.label,
      currentValue: props.value
    })
    
    // Fetch users using the meta API
    const response = await metaAPI.fetchPartnersTeamsUsers({
      team_ids: props.ownerTeamValue,
      search_all_partners: false,
      long_labels: true,
      page: 1,
      start: 0,
      limit: 25
    })
    
    // Update local array with formatted user data
    ownerUsers.value = (response.pl__partners_teams_users || []).map((user: any) => ({
      val: user.val,
      lbl: user.lbl,
      id: user.id,
      email: user.email,
      avatar: user.avatar || user.url_avatar,
      ...user
    }))
    
    console.log('👤 OwnerUserFormField: Fetched owner users:', ownerUsers.value)
    console.log('👤 OwnerUserFormField: Raw API response:', response)
    console.log('👤 OwnerUserFormField: Formatted options for field:', fieldOptions.value)
    
  } catch (error) {
    console.error('👤 OwnerUserFormField: Error fetching owner users:', error)
    ownerUsers.value = []
  } finally {
    loading.value = false
  }
}

// Watch for owner team changes and fetch users
watch(() => props.ownerTeamValue, (newTeamValue, oldTeamValue) => {
  console.log(`👤 OwnerUserFormField [${instanceId}]: Owner team changed:`, {
    oldTeamValue,
    newTeamValue,
    willFetch: !!newTeamValue,
    willClear: !newTeamValue
  })
  
  if (newTeamValue && newTeamValue !== oldTeamValue) {
    // Clear current value when team changes
    if (props.value) {
      console.log(`👤 OwnerUserFormField [${instanceId}]: Clearing user value due to team change`)
      emit('update', props.fieldName, null)
    }
    fetchOwnerUsers()
  } else if (!newTeamValue) {
    // Clear users when no team is selected
    ownerUsers.value = []
    if (props.value) {
      console.log(`👤 OwnerUserFormField [${instanceId}]: Clearing user value due to no team`)
      emit('update', props.fieldName, null)
    }
  }
}, { immediate: true })

// Fetch on mount if team is already selected
onMounted(() => {
  console.log(`👤 OwnerUserFormField [${instanceId}]: Component mounted:`, {
    hasOwnerTeam: !!props.ownerTeamValue,
    ownerTeamValue: props.ownerTeamValue,
    ownerUsersLength: ownerUsers.value.length,
    loading: loading.value,
    willFetch: !!(props.ownerTeamValue && ownerUsers.value.length === 0 && !loading.value)
  })
  
  if (props.ownerTeamValue && ownerUsers.value.length === 0 && !loading.value) {
    fetchOwnerUsers()
  }
})

// Handle field updates
function handleUpdate(fieldName: string, value: any) {
  emit('update', fieldName, value)
}

// Handle field saves
function handleSave(fieldName: string, value: any) {
  emit('save', fieldName, value)
}

// Handle cancel
function handleCancel() {
  emit('cancel')
}

// Handle "Assign to me" click
function assignToMe() {
  const currentUserId = userStore.userData?.id
  if (currentUserId && currentUserData.value && bravoFormFieldRef.value) {
    console.log(`👤 OwnerUserFormField [${instanceId}]: Assigning to current user:`, {
      userId: currentUserId,
      userData: currentUserData.value
    })
    
    // First, programmatically start editing the field to show the loading state
    const bravoField = bravoFormFieldRef.value
    if (bravoField && typeof bravoField.startEditing === 'function') {
      bravoField.startEditing()
    }
    
    // Update the internal value
    emit('update', props.fieldName, currentUserId)
    
    // Then trigger the save after a small delay to ensure the field is in edit mode
    setTimeout(() => {
      emit('save', props.fieldName, currentUserId)
    }, 100)
  }
}

// Handle save completion - forward to the underlying BravoFormField
function handleSaveComplete(success: boolean = true) {
  console.log(`👤 OwnerUserFormField [${instanceId}]: handleSaveComplete called with success:`, success)
  console.log(`👤 OwnerUserFormField [${instanceId}]: Field name:`, props.fieldName)
  console.log(`👤 OwnerUserFormField [${instanceId}]: Label:`, props.label)
  
  // Get reference to the BravoFormField component
  const bravoField = bravoFormFieldRef.value
  console.log(`👤 OwnerUserFormField [${instanceId}]: BravoFormField ref exists:`, !!bravoField)
  
  if (bravoField && typeof bravoField.handleSaveComplete === 'function') {
    console.log(`👤 OwnerUserFormField [${instanceId}]: Forwarding to BravoFormField.handleSaveComplete`)
    
    // Add a small delay to ensure the call happens after any pending updates
    setTimeout(() => {
      bravoField.handleSaveComplete(success)
      console.log(`👤 OwnerUserFormField [${instanceId}]: BravoFormField.handleSaveComplete called`)
    }, 10)
  } else {
    console.warn(`👤 OwnerUserFormField [${instanceId}]: BravoFormField ref not found or handleSaveComplete not available`)
  }
}

// Expose the handleSaveComplete method so parent components can call it
defineExpose({
  handleSaveComplete
})

// Debug: Watch fieldOptions changes
watch(fieldOptions, (newOptions) => {
  console.log('👤 OwnerUserFormField: fieldOptions changed:', {
    newOptionsLength: newOptions.length,
    newOptions: newOptions,
    fieldName: props.fieldName
  })
}, { deep: true })
</script>

<template>
  <BravoFormField
    ref="bravoFormFieldRef"
    :label="label"
    :field-name="fieldName"
    :value="value"
    :display-value="computedDisplayValue"
    input-type="dropdown"
    display-type="text"
    :options="fieldOptions"
    option-label="lbl"
    option-value="val"
    :is-loading="loading"
    :is-horizontal="isHorizontal"
    :is-editing="isEditing"
    :is-saving="isSaving"
    :no-value-text="noValueText"
    :data-test-id="dataTestId"
    show-clear
    @update="handleUpdate"
    @save="handleSave"
    @cancel="handleCancel"
  >
    <!-- Custom display template with avatar -->
    <template #display="{ displayValue }">
      <div class="user-display">
        <div v-if="selectedUserAvatar" class="avatar-container">
          <img
            :src="selectedUserAvatar"
            :alt="typeof displayValue === 'string' ? displayValue : 'User avatar'"
            class="user-avatar"
            @error="console.log('👤 Display avatar load error:', selectedUserAvatar)"
            @load="console.log('👤 Display avatar loaded:', selectedUserAvatar)"
          />
        </div>
        <div v-else class="avatar-placeholder">
          <i class="pi pi-user"></i>
        </div>
        <span>{{ displayValue }}</span>
      </div>
    </template>

    <!-- Custom option template with avatars -->
    <template #option="{ option }">
      <div class="user-option">
        <div v-if="option.url_avatar || option.avatar" class="avatar-container">
          <img
            :src="option.url_avatar || option.avatar"
            :alt="option.lbl"
            class="user-avatar"
            @error="console.log('👤 Avatar load error for option:', option)"
            @load="console.log('👤 Avatar loaded for option:', option)"
          />
        </div>
        <div v-else class="avatar-placeholder">
          <i class="pi pi-user"></i>
        </div>
        <span>{{ option.lbl }}</span>
      </div>
    </template>

  </BravoFormField>
  
  <!-- Assign to me button - outside the dropdown -->
  <div v-if="canAssignToMe" class="assign-to-me-container" :class="{ 'horizontal-mode': isHorizontal }">
    <button 
      type="button" 
      class="assign-to-me-button"
      @click="assignToMe"
    >
      <i class="pi pi-user-plus"></i>
      Assign to me
    </button>
  </div>
</template>

<style scoped>
.user-display {
  display: flex;
  align-items: center;
  gap: .5rem;
}

.user-option {
  display: flex;
  align-items: center;
  gap: .5rem;
}

.avatar-container,
.avatar-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-placeholder {
  background-color: #e2e8f0;
  color: #64748b;
}

.avatar-placeholder i {
  font-size: 12px;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.assign-to-me-container {
  margin-top: -1.25rem;
}

.assign-to-me-container.horizontal-mode {
  margin-top: 0;
  margin-left: 30%;
  padding-left: 1.5rem;
  margin-top: -1.25rem;
}

.assign-to-me-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem .25rem;
  transition: color 0.2s ease;
  text-align: left;
}

.assign-to-me-button:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.assign-to-me-button i {
  font-size: 13px;
}
</style>