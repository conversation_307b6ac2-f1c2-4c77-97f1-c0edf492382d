<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import type { SchemaField } from '@/composables/services/useIssuesAPI'
import type { Issue } from '@/services/IssuesAPI'
import { useMetaStore } from '@/stores/meta'
import { useCasesStore } from '@/stores/cases'
import { usePartnerStore } from '@/stores/partner'
import { blockConfigs, type BlockConfigField } from './block-configs'
import ProductFormField from './ProductFormField.vue'
import OwnerTeamFormField from './OwnerTeamFormField.vue'
import OwnerUserFormField from './OwnerUserFormField.vue'

interface Props {
  schemaFields: SchemaField[]
  issue?: Issue | null
  loading?: boolean
  readonly?: boolean
}

interface Emits {
  (e: 'field-change', field: string, value: any): void
  (e: 'save', changes: Record<string, any>): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  loading: false,
  readonly: true
})

const emit = defineEmits<Emits>()

// Store instances
const metaStore = useMetaStore()
const casesStore = useCasesStore()
const partnerStore = usePartnerStore()

// Responsive width tracking
const containerRef = ref<HTMLElement | null>(null)
const containerWidth = ref(0)
const isWideMode = computed(() => containerWidth.value >= 300) // Adjust this breakpoint as needed

// Members users state for the special field
const membersUsers = ref<any[]>([])
const loadingMembersUsers = ref(false)



// Track saving state per field
const savingFields = ref<Set<string>>(new Set())

// Track field refs for controlling edit/display mode
const fieldRefs = ref<Record<string, any>>({})

// Sort schema fields by position
const sortedFields = computed(() => {
  console.log('🔍 CaseInfoPanel: All schema fields received:', props.schemaFields.map(f => ({ field: f.field, lbl: f.lbl, fieldType: f.fieldType, type: f.type, position: f.position })))
  
  // Check specifically for resolution fields
  const resolutionFields = props.schemaFields.filter(f => f.field === 'c__d_resolution' || f.field === 'idr_resolution')
  console.log('🔍 CaseInfoPanel: Resolution fields found:', resolutionFields.map(f => ({ field: f.field, lbl: f.lbl, fieldType: f.fieldType })))
  
  // Check for custom fields
  const customFields = props.schemaFields.filter(f => f.field.includes('custom') || f.field.includes('dev_') || f.lbl.toLowerCase().includes('custom'))
  console.log('🔍 CaseInfoPanel: Custom fields found:', customFields.map(f => ({ field: f.field, lbl: f.lbl, fieldType: f.fieldType, type: f.type })))
  
  return [...props.schemaFields].sort((a, b) => a.position - b.position)
})

// Filter fields based on section visibility rules
const visibleFields = computed(() => {
  const filtered = sortedFields.value.filter(field => {
    // Always show section headers so we can conditionally render them
    if (isSectionHeader(field)) {
      return true
    }
    
    // Handle block configs (type 5)
    if (isBlockConfig(field)) {
      const blockConfigType = getBlockConfigType(field)
      if (!blockConfigType || !shouldRenderBlockConfig(blockConfigType)) {
        console.log('🔍 CaseInfoPanel: Hiding unsupported block config:', blockConfigType)
        return false
      }
      
      // Apply the same visibility rules as regular fields
      if (blockConfigType === 'resolve_issue' || blockConfigType === 'resolution_status') {
        if (!casesStore.isCurrentCaseResolvedOrClosed) {
          console.log('🔍 CaseInfoPanel: Hiding resolution block config:', blockConfigType, 'because case is not resolved/closed')
          return false
        }
      }
      
      if (blockConfigType === 'survey') {
        if (!casesStore.isCurrentCaseResolvedOrClosed) {
          console.log('🔍 CaseInfoPanel: Hiding survey block config:', blockConfigType, 'because case is not resolved/closed')
          return false
        }
      }
      
      return true
    }
    
    // For regular fields, check if they're part of a block config
    // Some fields can appear both in block configs AND as standalone fields
    const fieldsAllowedInBothPlaces = ['owner_partners_teams_id', 'owner_users_id']
    
    const fieldIsInBlockConfig = sortedFields.value.some(blockField => {
      if (!isBlockConfig(blockField)) return false
      const blockConfigType = getBlockConfigType(blockField)
      if (!blockConfigType) return false
      const blockFields = getBlockConfigFields(blockConfigType)
      return blockFields.some(bf => bf.field === field.field)
    })
    
    if (fieldIsInBlockConfig && !fieldsAllowedInBothPlaces.includes(field.field)) {
      console.log('🔍 CaseInfoPanel: Hiding field because it\'s part of a block config:', field.field)
      return false
    }
    
    // Hide Resolution section fields unless case is resolved/closed
    if (isResolutionSectionField(field) && !casesStore.isCurrentCaseResolvedOrClosed) {
      console.log('🔍 CaseInfoPanel: Hiding resolution field:', field.field, 'because case is not resolved/closed')
      return false
    }
    
    // Hide Survey section fields unless case is resolved/closed
    if (isSurveySectionField(field) && !casesStore.isCurrentCaseResolvedOrClosed) {
      console.log('🔍 CaseInfoPanel: Hiding survey field:', field.field, 'because case is not resolved/closed')
      return false
    }
    
    // Hide sponsor org and owner org fields if current partner is not a service provider
    if (isOrgField(field) && !partnerStore.isServiceProvider) {
      console.log('🔍 CaseInfoPanel: Hiding org field:', field.field, 'because current partner is not a service provider')
      return false
    }
    
    return true
  })
  
  console.log('🔍 CaseInfoPanel: Visible fields after filtering:', filtered.map(f => ({ field: f.field, lbl: f.lbl, type: f.type, isBlockConfig: isBlockConfig(f) })))
  return filtered
})

// Check if field belongs to Resolution section
function isResolutionSectionField(field: SchemaField): boolean {
  const resolutionFields = [
    'resolve_issue',
    'resolution_status', 
    'c__d_resolution',
    'idr_resolution',
    'resolution'
  ]
  return resolutionFields.includes(field.field)
}

// Check if field belongs to Survey section
function isSurveySectionField(field: SchemaField): boolean {
  // Add survey-related field names here
  const surveyFields = [
    'survey',
    'survey_rating',
    'survey_comments',
    'customer_satisfaction',
  ]
  return surveyFields.includes(field.field)
}

// Check if field is an organization field (sponsor org, owner org)
// Note: This excludes team and user fields which should always be visible
function isOrgField(field: SchemaField): boolean {
  const orgFields = [
    'sponsor_partners_id',     // Sponsor organization
    'owner_partners_id',       // Owner organization  
    'sponsor_org',
    'owner_org',
    'sponsor_organization',
    'owner_organization'
    // Explicitly NOT including:
    // - owner_partners_teams_id (team assignment)
    // - owner_users_id (user assignment)
    // - sponsor_partners_teams_id (team assignment)
    // - sponsor_users_id (user assignment)
  ]
  return orgFields.includes(field.field)
}

// Check if a section should be visible
function isSectionVisible(sectionName: string): boolean {
  if (!sectionName) return true
  
  const sectionLower = sectionName.toLowerCase()
  
  // Hide Resolution and Survey sections unless case is resolved/closed
  if (sectionLower.includes('resolution') || sectionLower.includes('survey')) {
    return casesStore.isCurrentCaseResolvedOrClosed
  }
  
  return true
}

// Type for field options
type FieldOption = {
  id?: string
  lbl: string
  val: string
  default?: boolean
}

// Map schema field names to issue data field names
function getIssueFieldName(schemaFieldName: string): string {
  const fieldMapping: Record<string, string> = {
    'resolution_status': 'idr_resolution',  // Schema field -> Issue data field (resolution notes)
    'resolve_issue': 'resolution'           // Schema field -> Issue data field (resolution status integer)
  }
  
  return fieldMapping[schemaFieldName] || schemaFieldName
}

// Get the value for a field from the issue data
function getFieldValue(field: SchemaField): any {
  if (!props.issue) return null
  
  // Map schema field name to actual issue data field name
  const issueFieldName = getIssueFieldName(field.field)
  let value = (props.issue as any)[issueFieldName]
  
  // Debug custom fields
  if (field.field.includes('custom') || field.field.includes('dev_') || (field.lbl && field.lbl.toLowerCase().includes('custom'))) {
    console.log('🔍 CaseInfoPanel: Custom field value lookup:', {
      fieldName: field.field,
      issueFieldName,
      value,
      availableKeys: Object.keys(props.issue).filter(key => key.toLowerCase().includes('custom') || key.includes('dev_'))
    })
  }
  
  // Special handling for idr_resolution - convert array to string if needed
  if (issueFieldName === 'idr_resolution') {
    if (Array.isArray(value)) {
      // Join array elements with newlines for textarea display
      return value.join('\n')
    }
    return value || ''
  }
  
  // Special handling for resolve_issue - ensure the value matches the dropdown options
  if (field.field === 'resolve_issue') {
    // Get the integer value from resolution field
    const integerValue = value
    
    // Get the options to find the matching value format
    const options = getFieldOptions(field)
    
    // Find the option that matches our integer value
    const matchingOption = options.find((opt: any) => {
      // Try both string and integer comparison
      return opt.val == integerValue || opt.val === String(integerValue)
    })
    
    // Return the value in the format expected by the dropdown
    return matchingOption ? matchingOption.val : integerValue
  }
  
  // Special handling for bc__tags_object_members_devices - handle array values
  if (field.field === 'bc__tags_object_members_devices') {
    // If it's an array, return as-is for multi-select
    if (Array.isArray(value)) {
      return value
    }
    // If it's a string that looks like JSON, try to parse it
    if (typeof value === 'string' && value.startsWith('[')) {
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    }
    return value
  }
  
  // Handle special cases for different field types
  if (field.fieldType === 'picklist_multi' && typeof value === 'string') {
    try {
      // Try to parse JSON string for multi-select fields
      value = JSON.parse(value)
    } catch {
      // If not JSON, split by comma
      value = value ? value.split(',').map((v: string) => v.trim()) : []
    }
  }
  
  return value
}

// Get the display value for read-only mode
function getDisplayValue(field: SchemaField): string | string[] {
  const value = getFieldValue(field)
  
  if (value === null || value === undefined || value === '' || value === 'null' || value === 'undefined' || (Array.isArray(value) && value.length === 0)) {
    return '—'
  }
  
  // Get the actual issue field name for special handling
  const issueFieldName = getIssueFieldName(field.field)
  
  switch (field.fieldType) {
    case 'picklist':
      // Find the option label for the value
      const options = getFieldOptions(field)
      const option = options.find((opt: FieldOption) => opt.val === value)
      return option ? option.lbl : value
    case 'picklist_multi':
      if (Array.isArray(value)) {
        const options = getFieldOptions(field)
        const labels = value.map(val => {
          const option = options.find((opt: FieldOption) => opt.val === val)
          return option ? option.lbl : val
        })
        return labels
      }
      return value
    case 'currency':
      if (typeof value === 'number') {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: field.decimalPlaces || 2,
          maximumFractionDigits: field.decimalPlaces || 2
        }).format(value)
      }
      return value
    case 'datetime':
      if (value) {
        return new Date(value).toLocaleString()
      }
      return value
    case 'date':
      if (value) {
        return new Date(value).toLocaleDateString()
      }
      return value
    default:
      // Special handling for members_users_id field
      if (field.field === 'members_users_id') {
        const user = membersUsers.value.find((user: any) => user.val === value)
        return user ? user.lbl : value
      }
      
      // Special handling for owner_partners_teams_id field - this will be handled by OwnerTeamFormField
      if (field.field === 'owner_partners_teams_id') {
        // Let the OwnerTeamFormField component handle the display value
        // by using the team options it fetches
        return value
      }
      

      
      // Special handling for category field
      if (field.field === 'category') {
        const categoryOptions = getFieldOptions(field)
        const category = categoryOptions.find((cat: FieldOption) => cat.val === value)
        return category ? category.lbl : value
      }
      
      // Special handling for resolution status field (resolve_issue -> resolution integer, but display c__d_resolution string)
      if (field.field === 'resolve_issue') {
        // For display, show the string value from c__d_resolution if available
        const stringValue = (props.issue as any)?.c__d_resolution
        if (stringValue) {
          return stringValue
        }
        // Fallback to finding the label from options using the integer value
        const resolutionOptions = getFieldOptions(field)
        const resolution = resolutionOptions.find((res: FieldOption) => res.val === value)
        return resolution ? resolution.lbl : value
      }
      
      // Special handling for resolution notes field (resolution_status -> idr_resolution)
      if (field.field === 'resolution_status') {
        // Resolution notes are just text, return as-is
        return value ? String(value) : '—'
      }
      
      return String(value)
  }
}

// Map schema field types to BravoFormField input types
function getInputType(field: SchemaField): 'text' | 'dropdown' | 'multiselect' | 'date' | 'datetime' {
  // Special case for resolve_issue - force it to be a dropdown
  if (field.field === 'resolve_issue') {
    return 'dropdown'
  }
  
  // Special case for product fields - force them to be multiselects
  if (field.field === 'bc__tags_object_members_devices') {
    return 'multiselect'
  }
  
  switch (field.fieldType) {
    case 'text':
    case 'textarea':
    case 'currency':
    case 'number':
      return 'text'
    case 'picklist':
      return 'dropdown'
    case 'picklist_multi':
      return 'multiselect'
    case 'datetime':
      return 'datetime'
    case 'date':
      return 'date'
    default:
      return 'text'
  }
}

// Get display type for BravoFormField
function getDisplayType(field: SchemaField): 'text' | 'chips' | 'tag' {
  if (field.fieldType === 'picklist_multi' || field.field === 'bc__tags_object_members_devices') {
    return 'chips'
  }
  return 'text'
}

// Get options for a field, with special handling for bc__tags_support
function getFieldOptions(field: SchemaField): FieldOption[] {
  // Special case for bc__tags_support - get from meta store
  if (field.field === 'bc__tags_support') {
    const tags = metaStore.metaData?.pl__tags || []
    const caseOwnerPartnersTeamsId = (props.issue as any)?.owner_partners_teams_id
    const caseCategoryId = (props.issue as any)?.category
    
    return tags
      .filter((tag: any) => {
        // First filter: category must be 3
        if (tag.category !== 3) return false
        
        // Second filter: check partners_teams_ids
        const partnersTeamsIds = tag.partners_teams_ids || []
        
        // If -1 is in the array, show this tag (universal tag)
        if (partnersTeamsIds.includes('-1')) {
          // Still need to check category_tag_ids even for universal tags
        } else if (caseOwnerPartnersTeamsId && partnersTeamsIds.includes(caseOwnerPartnersTeamsId)) {
          // Team-specific tag matches
        } else {
          // No team match, don't show this tag
          return false
        }
        
        // dont delete this - we may go off sponsior teams in the future or make it dynamic
        // const caseSponsorPartnersTeamsId = (props.issue as any)?.sponsor_partners_teams_id
        // if (caseSponsorPartnersTeamsId && partnersTeamsIds.includes(caseSponsorPartnersTeamsId)) {
        //   return true
        // }
        
        // Third filter: check category_tag_ids
        const categoryTagIds = tag.category_tag_ids || []
        
        // If category_tag_ids is empty, keep it in the list
        if (categoryTagIds.length === 0) return true
        
        // If it has values, validate that one of those categories matches our case.category
        if (caseCategoryId && categoryTagIds.includes(caseCategoryId)) {
          return true
        }
        
        // If no category match, don't show this tag
        return false
      })
      .map((tag: any) => ({
        lbl: tag.lbl,
        val: tag.val,
        id: tag.id
      }))
  }
  
  // Special case for category field - get from meta store
  if (field.field === 'category') {
    const tags = metaStore.metaData?.pl__tags || []
    return tags
      .filter((tag: any) => tag.category === 5)
      .map((tag: any) => ({
        lbl: tag.lbl,
        val: tag.val,
        id: tag.id
      }))
  }
  
  // Special case for resolution status (resolve_issue -> c__d_resolution) - get from meta store
  if (field.field === 'resolve_issue') {
    // Get resolution options from meta store
    const resolutionOptions = metaStore.metaData?.pl__issues_resolution || []
    const mappedOptions = resolutionOptions.map((resolution: any) => ({
      lbl: resolution.lbl,
      val: resolution.val,
      id: resolution.id
    }))
    
    return mappedOptions
  }
  
  // Special case for members_users_id - get from members users
  if (field.field === 'members_users_id') {
    return membersUsers.value.map((user: any) => ({
      lbl: user.lbl,
      val: user.val,
      id: user.id
    }))
  }
  

  
  // Default to schema options, mapping to the format expected by BravoFormField
  return (field.options || []).map(option => ({
    lbl: option.lbl,
    val: option.val,
    id: option.id
  }))
}

// Check if field should be displayed (handle type 2 fields which seem to be section headers)
function shouldDisplayField(field: SchemaField): boolean {
  // Type 2 seems to be section headers, type 1 and 5 are actual fields
  return field.type === 1 || field.type === 5
}

// Check if field is a product field
function isProductField(field: SchemaField): boolean {
  return field.field === 'bc__tags_object_members_devices' || field.field === 'product_picklist'
}

// Check if field is an owner team field
function isOwnerTeamField(field: SchemaField): boolean {
  return field.field === 'owner_partners_teams_id'
}

// Check if field is an owner user field
function isOwnerUserField(field: SchemaField): boolean {
  return field.field === 'owner_users_id'
}

// Check if field is a section header
function isSectionHeader(field: SchemaField): boolean {
  return field.type === 2
}

// Check if field is a block config (type 5)
function isBlockConfig(field: SchemaField): boolean {
  return field.type === 5
}

// Get the block config type for type 5 fields
function getBlockConfigType(field: SchemaField): string | null {
  if (!isBlockConfig(field)) return null
  return field.field
}

// Check if a block config should be rendered
function shouldRenderBlockConfig(blockConfigType: string): boolean {
  const config = blockConfigs[blockConfigType]
  if (!config) return false
  
  // Check visibility condition if it exists
  if (config.visibility) {
    return config.visibility.condition(props.issue, casesStore)
  }
  
  return true
}

// Get fields that should be rendered within a block config
function getBlockConfigFields(blockConfigType: string): BlockConfigField[] {
  const config = blockConfigs[blockConfigType]
  return config ? config.fields : []
}

// Get value for a block config field
function getBlockConfigFieldValue(blockField: BlockConfigField): any {
  if (!props.issue) return null
  return (props.issue as any)[blockField.field]
}

// Get display value for a block config field  
function getBlockConfigFieldDisplayValue(blockField: BlockConfigField): string | string[] {
  const value = getBlockConfigFieldValue(blockField)
  
  if (value === null || value === undefined || value === '') {
    return '—'
  }
  
  // Handle different display types
  if (blockField.displayType === 'chips' && Array.isArray(value)) {
    return value.map(v => String(v))
  }
  
  return String(value)
}

// Get options for a block config field
function getBlockConfigFieldOptions(blockField: BlockConfigField): FieldOption[] {
  // For now, return empty array - we'll implement data source loading later
  return []
}

// Handle field updates from BravoFormField
function handleFieldUpdate(fieldName: string, value: any) {
  // Get the actual issue field name for saving
  const issueFieldName = getIssueFieldName(fieldName)
  
  // Special handling for idr_resolution - convert string back to array format
  if (issueFieldName === 'idr_resolution') {
    // Convert string to array format for API compatibility
    const arrayValue = value ? [value] : []
    emit('field-change', issueFieldName, arrayValue)
    return
  }
  
  // Special handling for owner team changes - clear owner user when team changes
  if (fieldName === 'owner_partners_teams_id') {
    console.log('🔄 CaseInfoPanel: Owner team changed, clearing owner user')
    // Clear the owner user field since the user list will change
    emit('field-change', 'owner_users_id', null)
  }
  
  emit('field-change', issueFieldName, value)
}

// Handle field save from BravoFormField
async function handleFieldSave(fieldName: string, value: any) {
  if (!props.issue?.id) {
    console.error('CaseInfoPanel: No issue ID available for saving changes')
    return false
  }

  const issueFieldName = getIssueFieldName(fieldName)
  
  // Special handling for idr_resolution - convert string back to array format
  let finalValue = value
  if (issueFieldName === 'idr_resolution') {
    finalValue = value ? [value] : []
  }
  
  savingFields.value.add(fieldName)
  
  try {
    console.log('CaseInfoPanel: Starting save for field', fieldName, 'with value:', value)
    
    // Prepare the update object
    const updateData: any = {
      id: props.issue.id,
      [issueFieldName]: finalValue
    }
    
    // Special handling for owner team changes - also clear owner user in the same API call
    if (fieldName === 'owner_partners_teams_id') {
      console.log('🔄 CaseInfoPanel: Owner team changed, also clearing owner user in same API call')
      updateData.owner_users_id = null
    }
    
    // Call the cases store directly to update the case
    await casesStore.updateCase(updateData)
    
    console.log('CaseInfoPanel: Save successful, switching field back to display mode')
    
    // Manually trigger the switch back to display mode after successful save
    fieldRefs.value[fieldName]?.handleSaveComplete(true)
    
    // Also call handleSaveComplete on any block config instances of the same field
    Object.keys(fieldRefs.value).forEach(refKey => {
      if (refKey.includes(`-${fieldName}`) && refKey.startsWith('block-')) {
        fieldRefs.value[refKey]?.handleSaveComplete(true)
      }
    })
    
    // If we also cleared the owner user, make sure those fields exit edit mode too
    if (fieldName === 'owner_partners_teams_id') {
      fieldRefs.value['owner_users_id']?.handleSaveComplete(true)
      Object.keys(fieldRefs.value).forEach(refKey => {
        if (refKey.includes('-owner_users_id') && refKey.startsWith('block-')) {
          fieldRefs.value[refKey]?.handleSaveComplete(true)
        }
      })
    }
    
    // Add a small delay to ensure the UI update happens after the field state change
    await new Promise(resolve => setTimeout(resolve, 100))
    
    return true
  } catch (error) {
    console.error('CaseInfoPanel: Save failed for field', fieldName, ':', error)
    
    // Notify the field component of the failure
    fieldRefs.value[fieldName]?.handleSaveComplete(false)
    
    // Also call handleSaveComplete on any block config instances of the same field
    Object.keys(fieldRefs.value).forEach(refKey => {
      if (refKey.includes(`-${fieldName}`) && refKey.startsWith('block-')) {
        fieldRefs.value[refKey]?.handleSaveComplete(false)
      }
    })
    
    return false
  } finally {
    savingFields.value.delete(fieldName)
  }
}

// Watch for issue changes to fetch members users if needed
watch(() => [props.issue, props.schemaFields] as const, ([newIssue, newSchemaFields]) => {
  if (newIssue && newSchemaFields && Array.isArray(newSchemaFields)) {
    // Check if we have a members_users_id field in the schema and fetch data for display
    const hasMembersUsersField = newSchemaFields.some((field: SchemaField) => field.field === 'members_users_id')
    if (hasMembersUsersField && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
      fetchMembersUsers()
    }
    

  }
}, { immediate: true })

// ResizeObserver for responsive behavior
let resizeObserver: ResizeObserver | null = null

// Fetch data on component mount for display mode
onMounted(async () => {
  // Fetch members users if we have the required case data
  if (props.issue && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
    fetchMembersUsers()
  }
  
  // Set up responsive behavior
  await nextTick()
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        containerWidth.value = entry.contentRect.width
      }
    })
    resizeObserver.observe(containerRef.value)
    
    // Set initial width
    containerWidth.value = containerRef.value.offsetWidth
  }
})

// Clean up ResizeObserver
onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})



// Fetch members users based on case data
async function fetchMembersUsers() {
  if (!props.issue) return
  
  loadingMembersUsers.value = true
  try {
    // Extract required data from the case
    const members_id = (props.issue as any).members_id
    const members_locations_id = props.issue.members_locations_id
    const sponsor_id = props.issue.sponsor_partners_id || props.issue.owner_partners_id
    const context_org_id = props.issue.owner_partners_id || props.issue.sponsor_partners_id
    
    if (!members_id || !members_locations_id || !sponsor_id || !context_org_id) {
      console.warn('Missing required data for fetching members users:', {
        members_id,
        members_locations_id,
        sponsor_id,
        context_org_id
      })
      return
    }
    
    const response = await metaStore.fetchMembersUsers({
      members_id,
      members_locations_id,
      sponsor_id,
      context_org_id
    })
    
    membersUsers.value = response.pl__members_users || []
    console.log('Fetched members users:', membersUsers.value)
  } catch (error) {
    console.error('Failed to fetch members users:', error)
    membersUsers.value = []
  } finally {
    loadingMembersUsers.value = false
  }
}


</script>

<template>
  <div ref="containerRef" class="dynamic-case-form">
    <!-- Loading State -->
    <div v-if="loading" class="form-skeleton">
      <BravoSkeleton v-for="i in 6" :key="i" width="100%" height="40px" class="mb-4" />
    </div>
    
    <!-- Form Fields -->
    <div v-else class="form-fields">
      <template v-for="field in visibleFields" :key="field.field">
        <!-- Section Headers -->
        <div v-if="isSectionHeader(field) && isSectionVisible(field.lbl)" class="section-header">
          <h4>{{ field.lbl }}</h4>
        </div>
        
        <!-- Block Configs (Type 5) -->
        <div v-else-if="isBlockConfig(field)" class="block-config-container">
          <div class="block-config-fields">
            <template v-for="blockField in getBlockConfigFields(getBlockConfigType(field) || '')" :key="`block-${field.field}-${blockField.field}`">
              <!-- Owner Team Fields in Block Config using OwnerTeamFormField -->
              <OwnerTeamFormField
                v-if="blockField.field === 'owner_partners_teams_id'"
                :ref="el => { if (el) fieldRefs[`block-${field.field}-${blockField.field}`] = el }"
                :label="blockField.label"
                :field-name="blockField.field"
                :value="getBlockConfigFieldValue(blockField)"
                :display-value="getBlockConfigFieldDisplayValue(blockField)"
                :issue="issue"
                :is-horizontal="isWideMode"
                :is-editing="!readonly"
                :is-saving="savingFields.has(blockField.field)"
                :no-value-text="'—'"
                :data-test-id="`case-block-${field.field}-${blockField.field}`"
                @update="handleFieldUpdate"
                @save="handleFieldSave"
                @cancel="() => emit('cancel')"
                class="case-form-field block-config-field"
              />

              <!-- Owner User Fields in Block Config using OwnerUserFormField -->
              <OwnerUserFormField
                v-else-if="blockField.field === 'owner_users_id'"
                :ref="el => { if (el) fieldRefs[`block-${field.field}-${blockField.field}`] = el }"
                :label="blockField.label"
                :field-name="blockField.field"
                :value="getBlockConfigFieldValue(blockField)"
                :display-value="getBlockConfigFieldDisplayValue(blockField)"
                :issue="issue"
                :owner-team-value="getBlockConfigFieldValue({ field: 'owner_partners_teams_id' } as any)"
                :is-horizontal="isWideMode"
                :is-editing="!readonly"
                :is-saving="savingFields.has(blockField.field)"
                :no-value-text="'—'"
                :data-test-id="`case-block-${field.field}-${blockField.field}`"
                @update="handleFieldUpdate"
                @save="handleFieldSave"
                @cancel="() => emit('cancel')"
                class="case-form-field block-config-field"
              />
              
              <!-- Regular Block Config Fields using BravoFormField -->
              <BravoFormField
                v-else
                :ref="el => { if (el) fieldRefs[`block-${field.field}-${blockField.field}`] = el }"
                :label="blockField.label"
                :field-name="blockField.field"
                :value="getBlockConfigFieldValue(blockField)"
                :display-value="getBlockConfigFieldDisplayValue(blockField)"
                :input-type="blockField.inputType"
                :display-type="blockField.displayType"
                :options="getBlockConfigFieldOptions(blockField)"
                option-label="lbl"
                option-value="val"
                :is-loading="loadingMembersUsers"
                :is-horizontal="isWideMode"
                :is-editing="!readonly"
                :is-saving="savingFields.has(blockField.field)"
                :no-value-text="'—'"
                :data-test-id="`case-block-${field.field}-${blockField.field}`"
                @update="handleFieldUpdate"
                @save="handleFieldSave"
                @cancel="() => emit('cancel')"
                class="case-form-field block-config-field"
              />
            </template>
          </div>
        </div>
        
        <!-- Product Fields using ProductFormField -->
        <ProductFormField
          v-else-if="shouldDisplayField(field) && isProductField(field)"
          :ref="el => { if (el) fieldRefs[field.field] = el }"
          :label="field.lbl"
          :field-name="field.field"
          :value="getFieldValue(field)"
          :display-value="getDisplayValue(field)"
          :issue="issue"
          :is-horizontal="isWideMode"
          :is-editing="!readonly"
          :is-saving="savingFields.has(field.field)"
          :no-value-text="'—'"
          :data-test-id="`case-${field.field}`"
          @update="handleFieldUpdate"
          @save="handleFieldSave"
          @cancel="() => emit('cancel')"
          class="case-form-field"
        />

        <!-- Owner Team Fields using OwnerTeamFormField -->
        <OwnerTeamFormField
          v-else-if="shouldDisplayField(field) && isOwnerTeamField(field)"
          :ref="el => { if (el) fieldRefs[field.field] = el }"
          :label="field.lbl"
          :field-name="field.field"
          :value="getFieldValue(field)"
          :display-value="getDisplayValue(field)"
          :issue="issue"
          :is-horizontal="isWideMode"
          :is-editing="!readonly"
          :is-saving="savingFields.has(field.field)"
          :no-value-text="'—'"
          :data-test-id="`case-${field.field}`"
          @update="handleFieldUpdate"
          @save="handleFieldSave"
          @cancel="() => emit('cancel')"
          class="case-form-field"
        />

        <!-- Owner User Fields using OwnerUserFormField -->
        <OwnerUserFormField
          v-else-if="shouldDisplayField(field) && isOwnerUserField(field)"
          :ref="el => { if (el) fieldRefs[field.field] = el }"
          :label="field.lbl"
          :field-name="field.field"
          :value="getFieldValue(field)"
          :display-value="getDisplayValue(field)"
          :issue="issue"
          :owner-team-value="getFieldValue({ field: 'owner_partners_teams_id' } as SchemaField)"
          :is-horizontal="isWideMode"
          :is-editing="!readonly"
          :is-saving="savingFields.has(field.field)"
          :no-value-text="'—'"
          :data-test-id="`case-${field.field}`"
          @update="handleFieldUpdate"
          @save="handleFieldSave"
          @cancel="() => emit('cancel')"
          class="case-form-field"
        />
        
        <!-- Regular Fields using BravoFormField -->
        <BravoFormField
          v-else-if="shouldDisplayField(field)"
          :ref="el => { if (el) fieldRefs[field.field] = el }"
          :label="field.lbl"
          :field-name="field.field"
          :value="getFieldValue(field)"
          :display-value="getDisplayValue(field)"
          :input-type="getInputType(field)"
          :display-type="getDisplayType(field)"
          :options="getFieldOptions(field)"
          option-label="lbl"
          option-value="val"
          :is-loading="loadingMembersUsers"
          :is-horizontal="isWideMode"
          :is-editing="!readonly"
          :is-saving="savingFields.has(field.field)"
          :no-value-text="'—'"
          :data-test-id="`case-${field.field}`"
          @update="handleFieldUpdate"
          @save="handleFieldSave"
          @cancel="() => emit('cancel')"
          class="case-form-field"
        />
      </template>
    </div>
  </div>
</template>

<style scoped>
.dynamic-case-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-header {
  margin: 1.5rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-600);
}

.section-header h4 {
  margin: 0;
  color: var(--primary-600);
  font-size: 1.1rem;
  font-weight: 600;
}

.case-form-field {
  max-width: 500px; /* Limit maximum width for form fields */
}

/* Block config styling - seamless integration without borders */
.block-config-container {
  margin: 0;
  /* Remove border and background to blend with form */
}

.block-config-fields {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  /* Remove background and padding to match regular fields */
}

.block-config-field {
  max-width: 500px; /* Match the regular field width */
}

/* Ensure the form field labels are properly styled */
.case-form-field :deep(.article-field-label) {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

/* Add required indicator styling if needed */
.case-form-field :deep(.required-indicator) {
  color: var(--red-500);
  margin-left: 0.25rem;
}
</style> 