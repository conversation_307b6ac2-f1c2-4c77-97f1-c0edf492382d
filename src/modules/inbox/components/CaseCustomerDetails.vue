<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import Drawer from 'primevue/drawer'
import Button from 'primevue/button'
import { useSummarizerStore } from '@/stores/summarizer'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTitle3 from '@services/ui-component-library/components/BravoTypography/BravoTitle3.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import ZeroStateSearchSvg from '@/assets/zero-state-search.svg'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import CustomerInfoPanel from './customer-panels/CustomerInfoPanel.vue'
import CustomerLocationPanel from './customer-panels/CustomerLocationPanel.vue'
import CustomerProductsPanel from './customer-panels/CustomerProductsPanel.vue'
import CustomerCaseHistoryPanel from './customer-panels/CustomerCaseHistoryPanel.vue'
import CustomerContactsPanel from './customer-panels/CustomerContactsPanel.vue'
import ContactDrawerPanel from './customer-panels/ContactDrawerPanel.vue'
import ProductDrawerPanel from './customer-panels/ProductDrawerPanel.vue'

const props = defineProps<{ issue: any }>()

const emit = defineEmits<{
  (e: 'update:issue', issue: any): void
}>()

const expandedIndex = ref<number>(0) // 0 = Customer section open by default
const localIssue = ref(props.issue)

// Drawer state for contact details
const contactDrawerOpen = ref(false)
const selectedContact = ref<any>(null)

// Drawer state for product details
const productDrawerOpen = ref(false)
const selectedProduct = ref<any>(null)

const accordions = [
  { key: 'customer', label: 'Customer' },
  { key: 'location', label: 'Location' },
  { key: 'contacts', label: 'Contacts' },
  { key: 'products', label: 'Products' },
  { key: 'caseHistory', label: 'Case History' }
]

const summarizerStore = useSummarizerStore()



// Watch for prop changes and update local issue
watch(() => props.issue, (newIssue) => {
  localIssue.value = newIssue
}, { deep: true, immediate: true })

const member = computed(() => localIssue.value?.member || {})
const memberUser = computed(() => localIssue.value?.memberUser || {})

const customerName = computed(() =>
  memberUser.value.full_name ||
  member.value.name_legal ||
  member.value.name ||
  '—'
)

const memberId = computed(() => localIssue.value?.member?.id)
const orgId = computed(() => localIssue.value?.member?.context_org_id)

// Handle issue updates from child components
const handleIssueUpdate = (updatedIssue: any) => {
  localIssue.value = updatedIssue
  emit('update:issue', updatedIssue)
}

// Handle contact selection for drawer
const handleContactClick = (contact: any) => {
  selectedContact.value = contact
  contactDrawerOpen.value = true
}

// Close contact drawer
const closeContactDrawer = () => {
  contactDrawerOpen.value = false
  selectedContact.value = null
}

// Handle product selection for drawer
const handleProductClick = (product: any) => {
  selectedProduct.value = product
  productDrawerOpen.value = true
}

// Close product drawer
const closeProductDrawer = () => {
  productDrawerOpen.value = false
  selectedProduct.value = null
}

// Handle email action
const handleEmailContact = (email: string) => {
  const link = document.createElement('a')
  link.href = `mailto:${email}`
  link.click()
}

// Handle phone action
const handleCallContact = (phone: string) => {
  const link = document.createElement('a')
  link.href = `tel:${phone}`
  link.click()
}

const creatingSummary = computed(() => summarizerStore.createLoading)
const createSummaryError = computed(() => summarizerStore.createError)

const sentimentSeverity = computed(() => {
  const sentiment = summarizerStore.customerSummary?.sentiment?.toLowerCase()
  switch (sentiment) {
    case 'positive':
      return 'success'
    case 'neutral':
      return 'warn'
    case 'negative':
      return 'danger'
    case 'unknown':
      return 'info'
    default:
      return 'info'
  }
})

function getCustomerId() {
  return localIssue.value?.member.id
}

watch(() => getCustomerId(), (customerId) => {
  if (customerId) {
    summarizerStore.loadCustomerSummary(customerId)
  }
})

onMounted(async () => {
  const customerId = getCustomerId()
  if (customerId) {
    summarizerStore.loadCustomerSummary(customerId)
  }
  
  await nextTick()
  // Programmatically click the first accordion header if not open
  const firstHeader = document.querySelector('.p-accordionheader')
  if (firstHeader) {
    (firstHeader as HTMLElement).click()
  }
})



async function handleCreateSummary() {
  if (!memberId.value || !orgId.value || !customerName.value) return
  // Use a default date range (last 3 years)
  const endDate = new Date().toISOString().split('T')[0] + 'T00:00:00.00Z'
  const startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 3)).toISOString().split('T')[0] + 'T00:00:00.00Z'
  await summarizerStore.createCustomerSummary({
    orgId: orgId.value,
    customerId: memberId.value,
    customerName: customerName.value,
    startDate,
    endDate
  })
}
</script>
<template>
  <div class="case-customer-container" style="border-left-color: var(--border-color);">
    <!-- Fixed Header -->
    <div class="case-customer-header px-4 pt-5 pb-4 bg-white ">
      <BravoTitle1>Customer</BravoTitle1>
    </div>
    
    <!-- Scrollable Body -->
    <div class="case-customer-body">
      <div>
        <div v-if="summarizerStore.loading" class="text-slate-500 italic">Loading summary...</div>
        <template v-if="summarizerStore.error || !summarizerStore.customerSummary">
          <BravoZeroStateScreen
            title="No Customer Summary"
            message="No customer summary has been created yet. Generate a comprehensive summary of this customer's history and interactions."
            buttonLabel="Create Customer Summary"
            buttonIcon="pi pi-plus"
            :imageSrc="ZeroStateSearchSvg"
            imageAlt="No customer summary"
            :actionHandler="handleCreateSummary"
          />
          <div v-if="creatingSummary" class="text-slate-500 italic mt-2">Creating summary. This could take a while...</div>
          <div v-if="createSummaryError" class="text-red-500 mt-2">{{ createSummaryError }}</div>
        </template>
        <template v-else>
          <div class="p-4">
            <!-- Summary -->
            <div class="mb-5">
              <BravoTitle3 class="section-title">Summary</BravoTitle3>
              <div>
                {{ summarizerStore.customerSummary.summary || summarizerStore.customerSummary.text || 'No summary available.' }}
              </div>
            </div>

            <!-- Sentiment -->
            <div class="mb-5">
              <BravoTitle3 class="section-title">Sentiment</BravoTitle3>
              <div class="flex items-center gap-2">
                <BravoTag 
                  :severity="sentimentSeverity"
                  :value="summarizerStore.customerSummary.sentiment"
                />
                <span v-if="summarizerStore.customerSummary.most_common_issue" class="text-slate-600 text-sm">
                  <span class="font-medium">Most Common Issue:</span> {{ summarizerStore.customerSummary.most_common_issue }}
                </span>
              </div>
            </div>

            <!-- Engagement Level -->
            <div v-if="summarizerStore.customerSummary.engagement_level" class="mb-5">
              <BravoTitle3 class="section-title">Engagement Level</BravoTitle3>
              <div>{{ summarizerStore.customerSummary.engagement_level }}</div>
            </div>

            <!-- Open Issues -->
            <div v-if="summarizerStore.customerSummary.open_issues" class="mb-5">
              <BravoTitle3 class="section-title">Open Issues</BravoTitle3>
              <div v-if="summarizerStore.customerSummary.open_issues.length" class="ml-2">
                <ul class="list-disc pl-4">
                  <li v-for="(issue, idx) in summarizerStore.customerSummary.open_issues" :key="idx">
                    {{ issue }}
                  </li>
                </ul>
              </div>
              <div v-else class="ml-2">No open issues</div>
            </div>

            <!-- Trending Topics -->
            <div v-if="summarizerStore.customerSummary.trending_topics" class="mb-5">
              <BravoTitle3 class="section-title">Trending Topics</BravoTitle3>
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="(topic, idx) in summarizerStore.customerSummary.trending_topics" 
                  :key="idx"
                  class="bg-blue-100 text-blue-600 rounded px-2 py-0.5 text-xs font-semibold"
                >
                  {{ topic }}
                </span>
              </div>
            </div>

            <!-- Suggestions -->
            <div v-if="summarizerStore.customerSummary.suggestions" class="mb-5">
              <BravoTitle3 class="section-title">Suggestions</BravoTitle3>
              <ul class="list-disc pl-4 ml-2">
                <li v-for="(suggestion, idx) in summarizerStore.customerSummary.suggestions" :key="idx">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </template>

        <!-- Accordions -->
        <Accordion v-model="expandedIndex" multiple class="mt-2 w-full max-w-full overflow-hidden">
          <AccordionPanel v-for="(section, idx) in accordions" :key="section.key" :value="idx">
            <AccordionHeader>
              <div v-if="section.key === 'summary'" class="case-pane-tab-header">{{ section.label }}</div>
              <span v-else class="font-semibold text-slate-700">{{ section.label }}</span>
            </AccordionHeader>
            <AccordionContent class="w-full max-w-full overflow-hidden">
              <CustomerInfoPanel v-if="section.key === 'customer'" :issue="localIssue" @update:issue="handleIssueUpdate" />
              <CustomerLocationPanel v-else-if="section.key === 'location'" :issue="localIssue" @update:issue="handleIssueUpdate" />
              <CustomerContactsPanel v-else-if="section.key === 'contacts'" :issue="localIssue" @contact-click="handleContactClick" />
              <CustomerProductsPanel v-else-if="section.key === 'products'" :issue="localIssue" @product-click="handleProductClick" />
              <CustomerCaseHistoryPanel v-else-if="section.key === 'caseHistory'" :issue="localIssue" />
            </AccordionContent>
          </AccordionPanel>
        </Accordion>
      </div>
    </div>

    <!-- Contact Details Drawer -->
    <Drawer 
      v-model:visible="contactDrawerOpen" 
      header="Contact Details"
      position="left"
      class="contact-drawer"
      style="width: 400px"
    >
      <template #header>
        <div class="flex items-center justify-between w-full">
          <h3 class="text-lg font-semibold text-slate-700">Contact Details</h3>
        </div>
      </template>

      <ContactDrawerPanel 
        :contact="selectedContact"
        @email-contact="handleEmailContact"
        @call-contact="handleCallContact"
      />
    </Drawer>

    <!-- Product Details Drawer -->
    <Drawer 
      v-model:visible="productDrawerOpen" 
      header="Product Details"
      position="left"
      class="product-drawer"
      style="width: 400px"
    >
      <template #header>
        <div class="flex items-center justify-between w-full">
          <h3 class="text-lg font-semibold text-slate-700">Product Details</h3>
        </div>
      </template>

      <ProductDrawerPanel :product="selectedProduct" />
    </Drawer>
  </div>
</template>
<style scoped>
.case-customer-container {
  height: 100%;
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  border-left: 1px solid var(--border-color);
  overflow: hidden;
}
.case-customer-header {
  flex-shrink: 0;
  background: white;
  display: flex;
  align-items: center;
}
.case-customer-body {
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background: white;
  min-height: 0;
}
.min-h-full { min-height: 100%; }
.case-pane-tab-header {
  font-size: 1.125rem;
  font-weight: 600;
  color: #22223b;
  padding: 0.75rem 1rem 0.5rem 1rem;
  border-bottom: 2px solid #e5e7eb;
  background: #fff;
  margin-bottom: 0;
  letter-spacing: 0.01em;
}
.section-title {
  padding-bottom: 0.4rem;
}

/* Drawer styles */
.contact-drawer,
.product-drawer {
  z-index: 1100;
}
</style> 