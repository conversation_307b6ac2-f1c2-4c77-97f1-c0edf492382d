import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';

export interface MetaResponse {
    success: boolean;
    message?: string;
    [key: string]: any;
}

export interface IssuePartnerTeamsResponse {
    success: boolean;
    message?: string;
    pl__issues_partners: Array<{
        val: string;
        lbl: string;
        members_locations_id: string;
        partners_id: string;
        id: string;
    }>;
    pl__issues_partners_teams: Array<{
        val: string;
        lbl: string;
        members_locations_id: string;
        partners_id: string;
        partners_teams_id: string;
        can_support: boolean;
        external: boolean;
        id: string;
    }>;
    current_server_time: string;
}

export interface MembersUsersResponse {
    success: boolean;
    message?: string;
    pl__members_users: Array<{
        val: string;
        id: string;
        members_id: string;
        email: string;
        lbl: string;
        public_name: string;
    }>;
    current_server_time: string;
}

export interface MemberProductsResponse {
    success: boolean;
    message?: string;
    pl__members_devices: Array<{
        val: string;
        id: string;
        nickname: string;
        members_id: string;
        members_locations_id: string;
        type: string;
        category: string;
        lbl: string;
    }>;
    current_server_time: string;
}

export interface FetchMembersUsersParams {
    members_id: string;
    members_locations_id: string;
    sponsor_id: string;
    context_org_id: string;
}

export interface FetchMemberProductsParams {
    members_id: string;
    members_locations_id: string;
}

export interface MembersLocationsPartnersResponse {
    success: boolean;
    message?: string;
    pl__members_partners_teams: Array<{
        val: string;
        lbl: string;
        members_locations_id: string;
        partners_id: string;
        partners_teams_id: string;
        can_support: boolean;
        external: boolean;
        id: string;
    }>;
    pl__members_partners: Array<{
        val: string;
        lbl: string;
        members_locations_id: string;
        partners_id: string;
        id: string;
    }>;
    current_server_time: string;
}

export interface FetchMembersLocationsPartnersParams {
    members_locations_id: string;
    orig_team_id?: string;
}

export interface PartnersTeamsUsersResponse {
    success: boolean;
    message?: string;
    pl__partners_teams_users: Array<{
        val: string;
        id: string;
        lbl: string;
        url_avatar: string;
        email: string;
        lbl_role: string;
        partners_id: string;
        avatar?: string;
    }>;
    current_server_time: string;
}

export interface FetchPartnersTeamsUsersParams {
    team_ids: string;
    search_all_partners?: boolean;
    long_labels?: boolean;
    page?: number;
    start?: number;
    limit?: number;
}

export interface CannedResponsesResponse {
    success: boolean;
    message?: string;
    canned_responses?: {
        totalCount: number;
        results: Array<{
            id: string;
            partners_id: string;
            users_id: string;
            partners_teams_ids: string[];
            name: string;
            content: string;
            data: any;
            status: number;
            updated: string;
            created: string;
            metadata: any;
            _title: string;
            c__owner_partner_name: string;
            c__owner_partner_nickname: string;
            c__d_status: string;
            c__creator_user_name: string;
            c__creator_nickname: string;
            c__teams_names: string[];
            c__data: string;
            _highlighted: boolean;
            _highlightmap: any;
            _has_detail: boolean;
            _canWrite: boolean;
            _uiAccess: {
                edit: boolean;
                delete: boolean;
                clone: boolean;
                merge: boolean;
            };
        }>;
    };
    // Legacy format for backward compatibility
    pl__canned_responses?: Array<{
        val: string;
        lbl: string;
        content: string;
        text: string;
        partners_id: string;
        partners_teams_ids: string[];
        users_id: string;
        upload_files: any;
        upload_files_names: any;
        id: string;
    }>;
    results?: Array<{
        val: string;
        lbl: string;
        content: string;
        text: string;
        partners_id: string;
        partners_teams_ids: string[];
        users_id: string;
        upload_files: any;
        upload_files_names: any;
        id: string;
    }>;
    current_server_time?: string;
    totalCount?: number;
}

export interface FetchCannedResponsesParams {
    object: string;
    object_id: string;
}

/**
 * Composable that provides access to Meta API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useMetaAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchMetaData(): Promise<any> {
            try {            
                const data = await httpClient.get<MetaResponse>('admin/v4/core/', {
                    sAction: 'meta'
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch metadata', data);
                throw new Error(data.message || 'Failed to fetch metadata');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching metadata:', error);
                throw error;
            }
        },

        async fetchPartnerMetaData(): Promise<any> {
            try {            
                const data = await httpClient.get<MetaResponse>('admin/v4/core/', {
                    sAction: 'metaPartners'
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch partner metadata', data);
                throw new Error(data.message || 'Failed to fetch partner metadata');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching partner metadata:', error);
                throw error;
            }
        },

        async fetchIssuePartnerTeams(membersLocationId: string, issuesId: string): Promise<IssuePartnerTeamsResponse> {
            try {
                const filter = JSON.stringify([
                    { property: 'members_locations_id', value: membersLocationId },
                    { property: 'issues_id', value: issuesId }
                ]);
                
                const data = await httpClient.get<IssuePartnerTeamsResponse>('admin/v4/core/', {
                    sAction: 'metaIssuePartnerTeams',
                    page: '1',
                    start: '0',
                    limit: '25',
                    filter: filter
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch issue partner teams', data);
                throw new Error(data.message || 'Failed to fetch issue partner teams');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching issue partner teams:', error);
                throw error;
            }
        },

        async fetchMembersUsers(params: FetchMembersUsersParams): Promise<MembersUsersResponse> {
            try {
                const query = JSON.stringify([
                    { property: 'members_id', value: params.members_id },
                    { property: 'members_locations_id', value: params.members_locations_id },
                    { property: 'sponsor_id', value: params.sponsor_id },
                    { property: 'context_org_id', value: params.context_org_id }
                ]);
                
                const data = await httpClient.get<MembersUsersResponse>('admin/v4/core/', {
                    sAction: 'metaMembersUsers',
                    page: '1',
                    start: '0',
                    limit: '25',
                    query: query
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch members users', data);
                throw new Error(data.message || 'Failed to fetch members users');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching members users:', error);
                throw error;
            }
        },

        async fetchMemberProducts(params: FetchMemberProductsParams): Promise<MemberProductsResponse> {
            try {
                const filter = JSON.stringify([
                    { property: 'members_id', value: params.members_id },
                    { property: 'members_locations_id', value: params.members_locations_id }
                ]);
                
                const data = await httpClient.get<MemberProductsResponse>('admin/v4/core/', {
                    sAction: 'metaMembersDevices',
                    page: '1',
                    start: '0',
                    limit: '25',
                    filter: filter
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch member products', data);
                throw new Error(data.message || 'Failed to fetch member products');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching member products:', error);
                throw error;
            }
        },

        async fetchCannedResponses(params: FetchCannedResponsesParams): Promise<CannedResponsesResponse> {
            try {
                // Get current user's partner ID from user store
                const userStore = useUserStore();
                const partnerId = userStore.userTeams?.[0]?.team_id?.substring(0, 3) || userStore.userTeams?.[0]?.substring(0, 3) || 'H3F'; // Extract partner ID from team or fallback
                
                const filter = JSON.stringify([
                    { property: 'id', value: '_no_filter_' },
                    { property: 'partners_id', value: partnerId }
                ]);
                
                const data = await httpClient.get<CannedResponsesResponse>('admin/v4/canned_responses/', {
                    sAction: 'listing',
                    page: '1',
                    start: '0',
                    limit: '50',
                    filter: filter
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch canned responses', data);
                throw new Error(data.message || 'Failed to fetch canned responses');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching canned responses:', error);
                throw error;
            }
        },

        async fetchMembersLocationsPartners(params: FetchMembersLocationsPartnersParams): Promise<MembersLocationsPartnersResponse> {
            try {
                const filterArray = [
                    { property: 'members_locations_id', value: params.members_locations_id }
                ];
                
                // Add orig_team_id filter if provided
                if (params.orig_team_id) {
                    filterArray.push({ property: 'orig_team_id', value: params.orig_team_id });
                }
                
                const filter = JSON.stringify(filterArray);
                
                const data = await httpClient.get<MembersLocationsPartnersResponse>('admin/v4/core/', {
                    sAction: 'metaMembersLocationsPartners',
                    page: '1',
                    start: '0',
                    limit: '25',
                    filter: filter
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch members locations partners', data);
                throw new Error(data.message || 'Failed to fetch members locations partners');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching members locations partners:', error);
                throw error;
            }
        },

        async fetchPartnersTeamsUsers(params: FetchPartnersTeamsUsersParams): Promise<PartnersTeamsUsersResponse> {
            try {
                const filter = JSON.stringify([
                    { property: 'team_ids', value: [params.team_ids] }
                ]);
                
                const data = await httpClient.get<PartnersTeamsUsersResponse>('admin/v4/core/', {
                    sAction: 'metaPartnersTeamsUsers',
                    search_all_partners: String(params.search_all_partners ?? false),
                    long_labels: String(params.long_labels ?? true),
                    page: String(params.page ?? 1),
                    start: String(params.start ?? 0),
                    limit: String(params.limit ?? 25),
                    filter: filter
                });

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch partners teams users', data);
                throw new Error(data.message || 'Failed to fetch partners teams users');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching partners teams users:', error);
                throw error;
            }
        }
    };
} 