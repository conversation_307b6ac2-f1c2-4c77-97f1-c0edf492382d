import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';
import type { Product, ProductListResponse, PartnerResponse } from '@/types/partner';

/**
 * Composable that provides access to Partner API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pi<PERSON> is ready.
 */
export function usePartnerAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchProducts(): Promise<ProductListResponse> {
            try {
                const response = await httpClient.get('admin/v4/partners/', {
                    sAction: 'listingTemplate',
                    page: '1',
                    start: '0',
                    limit: '1000'
                });
                return response as ProductListResponse;
            } catch (error) {
                console.error('❌ PartnerAPI: Error fetching products:', error);
                throw error;
            }
        },

        async fetchPartnerTeams(ecosystem: boolean = false): Promise<any> {
            try {
                const response = await httpClient.get('admin/v4/core/', {
                    sAction: 'metaPartnersTeams'
                });
                return response;
            } catch (error) {
                console.error('❌ PartnerAPI: Error fetching partner teams:', error);
                throw error;
            }
        },

        async fetchCurrentPartner(partnerId: string): Promise<PartnerResponse> {
            try {
                const response = await httpClient.get('api/partners/', {
                    sAction: 'listing',
                    context_org_id: '',
                    id: partnerId
                });
                return response as PartnerResponse;
            } catch (error) {
                console.error('❌ PartnerAPI: Error fetching current partner:', error);
                throw error;
            }
        }
    };
} 