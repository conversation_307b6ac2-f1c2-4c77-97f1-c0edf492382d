import { ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

/**
 * Composable for managing remember me functionality
 */
export function useRememberMe() {
    const authStore = useAuthStore();
    const rememberedEmail = ref('');
    const isRemembered = ref(false);

    /**
     * Get the remembered email from localStorage
     */
    function getRememberedEmail(): string {
        try {
            const email = localStorage.getItem('rememberedEmail') || '';
            console.debug('🔐 RememberMe: Retrieved email from localStorage:', email);
            return email;
        } catch (error) {
            console.warn('🔐 RememberMe: Failed to get remembered email from localStorage:', error);
            return '';
        }
    }

    /**
     * Set the remembered email in localStorage
     */
    function setRememberedEmail(email: string): void {
        try {
            if (email) {
                localStorage.setItem('rememberedEmail', email);
                console.debug('🔐 RememberMe: Stored email in localStorage:', email);
            } else {
                localStorage.removeItem('rememberedEmail');
                console.debug('🔐 RememberMe: Removed email from localStorage');
            }
        } catch (error) {
            console.warn('🔐 RememberMe: Failed to set remembered email in localStorage:', error);
        }
    }

    /**
     * Check if remember me cookie exists and load remembered email
     */
    function checkRememberMe(): void {
        isRemembered.value = authStore.hasRememberMeCookie();
        console.debug('🔐 RememberMe: Cookie exists:', isRemembered.value);
        
        if (isRemembered.value) {
            rememberedEmail.value = getRememberedEmail();
            console.debug('🔐 RememberMe: Loaded remembered email:', rememberedEmail.value);
        }
    }

    /**
     * Handle remember me on successful login
     */
    function handleRememberMe(email: string, shouldRemember: boolean): void {
        console.debug('🔐 RememberMe: Handling remember me for email:', email, 'shouldRemember:', shouldRemember);
        if (shouldRemember) {
            setRememberedEmail(email);
            authStore.setRememberMeCookie();
            console.debug('🔐 RememberMe: Set remember me for email:', email);
        } else {
            // If user unchecks remember me, clear the stored email
            setRememberedEmail('');
            authStore.clearRememberMeCookie();
            console.debug('🔐 RememberMe: Cleared remember me');
        }
    }

    // Initialize on mount
    onMounted(() => {
        console.debug('🔐 RememberMe: Composable mounted, checking remember me status');
        checkRememberMe();
    });

    return {
        rememberedEmail,
        isRemembered,
        getRememberedEmail,
        setRememberedEmail,
        checkRememberMe,
        handleRememberMe
    };
} 