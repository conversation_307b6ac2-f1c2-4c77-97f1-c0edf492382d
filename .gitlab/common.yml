.env_stage:
  environment:
    name: stage
    deployment_tier: development
    url: https://appv5.stage-ovationcxm.app
  variables:
    VITE_BOOMTOWN_API_HOST: https://api.stage.goboomtown.com

.env_preprod:
  environment:
    name: preprod
    deployment_tier: staging
    url: https://appv5.preprod-ovationcxm.app
  variables:
    VITE_BOOMTOWN_API_HOST: https://api.preprod.goboomtown.com

.env_prod:
  environment:
    name: prod
    deployment_tier: production
    url: https://appv5.ovationcxm.app
  variables:
    VITE_BOOMTOWN_API_HOST: https://api.prod.goboomtown.com

.rules_preview:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

.rules_deploy:
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+\.[0-9]+\.[0-9]+$/'
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == null'

.build:
  retry: 1
  image: node:latest
  cache:
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - node_modules
    policy: pull
  variables:
    NODE_OPTIONS: --max-old-space-size=32768
  script:
    - npm run build
    - rm -rf dist/_redirects
  dependencies:
    - cache_dependencies
  artifacts:
    paths:
      - dist/
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.deploy_gcs:
  image: google/cloud-sdk
  script:
    - echo $GOOGLE_PROJECT_ID
    - echo $GCS_BUCKET_URL
    - gcloud auth activate-service-account --key-file $GOOGLE_APPLICATION_CREDENTIALS
    - gcloud config set project $GOOGLE_PROJECT_ID
    - gcloud storage rsync -r dist "${GCS_BUCKET_URL}/deploy"
    - gcloud storage cp -r "${GCS_BUCKET_URL}/public/*" "${GCS_BUCKET_URL}/backup" || echo "/public doesnt exist yet"
    - gcloud storage cp -r "${GCS_BUCKET_URL}/deploy/*" "${GCS_BUCKET_URL}/public"
    - gcloud compute url-maps invalidate-cdn-cache $LOAD_BALANCER_NAME --host=${PUB_HOSTNAME} --path="/*"
    - gcloud storage rm -r "${GCS_BUCKET_URL}/deploy"

.deploy_preview_gcs:
  image: google/cloud-sdk
  script:
    - echo $GOOGLE_PROJECT_ID
    - echo $GCS_PREVIEW_BUCKET_URL
    - gcloud auth activate-service-account --key-file $GOOGLE_APPLICATION_CREDENTIALS
    - gcloud config set project $GOOGLE_PROJECT_ID
    - gcloud storage rsync -r dist "${GCS_PREVIEW_BUCKET_URL}/${DEPLOY_FOLDER_NAME}"

.destroy_preview_gcs:
  image: google/cloud-sdk
  script:
    - echo $GOOGLE_PROJECT_ID
    - echo $GCS_PREVIEW_BUCKET_URL
    - gcloud auth activate-service-account --key-file $GOOGLE_APPLICATION_CREDENTIALS
    - gcloud config set project $GOOGLE_PROJECT_ID
    - gcloud storage rm -r "${GCS_PREVIEW_BUCKET_URL}/${DEPLOY_FOLDER_NAME}/**" || true

.test_e2e:
  cache:
    key:
      files:
        - package-lock.json
        - package.json
    paths:
      - node_modules
    policy: pull
  needs: [cache_dependencies]
  image: mcr.microsoft.com/playwright:v1.51.1-noble
  script:
    - env | sort
    - npx playwright install --with-deps
    - npx playwright test --reporter=dot

.upload_sourcemaps_datadog:
  stage: postdeploy
  image: node:latest
  script:
    - export APP_VERSION=$(node -p "require('./package.json').version")
    - DD_VERSION=${APP_VERSION#v}
    - echo $DD_VERSION
    - find $PWD/dist -type f -name "*.js.map" | wc -l
    - npm install -g @datadog/datadog-ci
    - |
      datadog-ci sourcemaps upload $PWD/dist \
        --service appv5 \
        --release-version $DD_VERSION \
        --minified-path-prefix https://$PUB_HOSTNAME
